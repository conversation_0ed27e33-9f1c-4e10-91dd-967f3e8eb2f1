using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Comprehensive upgrade system for all player equipment and vehicles
/// </summary>
public class UpgradeSystem : MonoBehaviour
{
    [Header("Upgrade Settings")]
    [SerializeField] private List<UpgradeCategory> upgradeCategories = new List<UpgradeCategory>();
    [SerializeField] private int playerCredits = 1000;
    [SerializeField] private int playerScrap = 50;
    
    public static UpgradeSystem Instance { get; private set; }
    
    // Events
    public System.Action<UpgradeItem> OnUpgradePurchased;
    public System.Action<int> OnCreditsChanged;
    public System.Action<int> OnScrapChanged;
    
    // Properties
    public int Credits => playerCredits;
    public int Scrap => playerScrap;
    
    private Dictionary<string, int> upgradeLevels = new Dictionary<string, int>();
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeUpgrades();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void InitializeUpgrades()
    {
        // Initialize all upgrade levels to 0
        foreach (var category in upgradeCategories)
        {
            foreach (var upgrade in category.upgrades)
            {
                upgradeLevels[upgrade.upgradeId] = 0;
            }
        }
        
        // Create default upgrade categories if none exist
        if (upgradeCategories.Count == 0)
        {
            CreateDefaultUpgrades();
        }
    }
    
    private void CreateDefaultUpgrades()
    {
        // Flashlight Upgrades
        var flashlightCategory = new UpgradeCategory
        {
            categoryName = "Flashlight",
            categoryIcon = null,
            upgrades = new List<UpgradeItem>
            {
                new UpgradeItem
                {
                    upgradeId = "flashlight_brightness",
                    upgradeName = "Brightness",
                    description = "Increases flashlight brightness",
                    maxLevel = 5,
                    baseCost = 100,
                    baseScrapCost = 10,
                    costMultiplier = 1.5f,
                    upgradeType = UpgradeType.Flashlight
                },
                new UpgradeItem
                {
                    upgradeId = "flashlight_battery",
                    upgradeName = "Battery Life",
                    description = "Extends flashlight battery duration",
                    maxLevel = 5,
                    baseCost = 150,
                    baseScrapCost = 15,
                    costMultiplier = 1.4f,
                    upgradeType = UpgradeType.Flashlight
                },
                new UpgradeItem
                {
                    upgradeId = "flashlight_range",
                    upgradeName = "Range",
                    description = "Increases flashlight beam distance",
                    maxLevel = 3,
                    baseCost = 200,
                    baseScrapCost = 20,
                    costMultiplier = 1.6f,
                    upgradeType = UpgradeType.Flashlight
                }
            }
        };
        
        // Weapon Upgrades
        var weaponCategory = new UpgradeCategory
        {
            categoryName = "Weapons",
            categoryIcon = null,
            upgrades = new List<UpgradeItem>
            {
                new UpgradeItem
                {
                    upgradeId = "weapon_damage",
                    upgradeName = "Damage",
                    description = "Increases weapon damage",
                    maxLevel = 10,
                    baseCost = 200,
                    baseScrapCost = 25,
                    costMultiplier = 1.3f,
                    upgradeType = UpgradeType.Weapon
                },
                new UpgradeItem
                {
                    upgradeId = "weapon_ammo",
                    upgradeName = "Ammo Capacity",
                    description = "Increases maximum ammo capacity",
                    maxLevel = 5,
                    baseCost = 150,
                    baseScrapCost = 20,
                    costMultiplier = 1.4f,
                    upgradeType = UpgradeType.Weapon
                },
                new UpgradeItem
                {
                    upgradeId = "weapon_reload",
                    upgradeName = "Reload Speed",
                    description = "Faster weapon reload times",
                    maxLevel = 3,
                    baseCost = 300,
                    baseScrapCost = 30,
                    costMultiplier = 1.5f,
                    upgradeType = UpgradeType.Weapon
                }
            }
        };
        
        // Spacesuit Upgrades
        var spacesuitCategory = new UpgradeCategory
        {
            categoryName = "Spacesuit",
            categoryIcon = null,
            upgrades = new List<UpgradeItem>
            {
                new UpgradeItem
                {
                    upgradeId = "suit_oxygen",
                    upgradeName = "Oxygen Capacity",
                    description = "Increases oxygen tank capacity",
                    maxLevel = 5,
                    baseCost = 250,
                    baseScrapCost = 30,
                    costMultiplier = 1.4f,
                    upgradeType = UpgradeType.Spacesuit
                },
                new UpgradeItem
                {
                    upgradeId = "suit_protection",
                    upgradeName = "Armor Protection",
                    description = "Reduces damage taken",
                    maxLevel = 5,
                    baseCost = 300,
                    baseScrapCost = 35,
                    costMultiplier = 1.5f,
                    upgradeType = UpgradeType.Spacesuit
                },
                new UpgradeItem
                {
                    upgradeId = "suit_mobility",
                    upgradeName = "Mobility Enhancement",
                    description = "Increases movement speed",
                    maxLevel = 3,
                    baseCost = 400,
                    baseScrapCost = 40,
                    costMultiplier = 1.6f,
                    upgradeType = UpgradeType.Spacesuit
                }
            }
        };
        
        // Spacecraft Upgrades
        var spacecraftCategory = new UpgradeCategory
        {
            categoryName = "Spacecraft",
            categoryIcon = null,
            upgrades = new List<UpgradeItem>
            {
                new UpgradeItem
                {
                    upgradeId = "ship_engine",
                    upgradeName = "Engine Power",
                    description = "Increases ship speed and acceleration",
                    maxLevel = 5,
                    baseCost = 500,
                    baseScrapCost = 50,
                    costMultiplier = 1.5f,
                    upgradeType = UpgradeType.Spacecraft
                },
                new UpgradeItem
                {
                    upgradeId = "ship_fuel",
                    upgradeName = "Fuel Efficiency",
                    description = "Reduces fuel consumption",
                    maxLevel = 5,
                    baseCost = 400,
                    baseScrapCost = 40,
                    costMultiplier = 1.4f,
                    upgradeType = UpgradeType.Spacecraft
                },
                new UpgradeItem
                {
                    upgradeId = "ship_shields",
                    upgradeName = "Shield Generator",
                    description = "Adds protective shields to spacecraft",
                    maxLevel = 3,
                    baseCost = 800,
                    baseScrapCost = 80,
                    costMultiplier = 1.8f,
                    upgradeType = UpgradeType.Spacecraft
                },
                new UpgradeItem
                {
                    upgradeId = "ship_cargo",
                    upgradeName = "Cargo Capacity",
                    description = "Increases inventory space",
                    maxLevel = 5,
                    baseCost = 300,
                    baseScrapCost = 30,
                    costMultiplier = 1.3f,
                    upgradeType = UpgradeType.Spacecraft
                }
            }
        };
        
        upgradeCategories.Add(flashlightCategory);
        upgradeCategories.Add(weaponCategory);
        upgradeCategories.Add(spacesuitCategory);
        upgradeCategories.Add(spacecraftCategory);
    }
    
    public bool CanPurchaseUpgrade(string upgradeId)
    {
        UpgradeItem upgrade = GetUpgrade(upgradeId);
        if (upgrade == null) return false;
        
        int currentLevel = GetUpgradeLevel(upgradeId);
        if (currentLevel >= upgrade.maxLevel) return false;
        
        int cost = GetUpgradeCost(upgradeId);
        int scrapCost = GetUpgradeScrapCost(upgradeId);
        
        return playerCredits >= cost && playerScrap >= scrapCost;
    }
    
    public bool PurchaseUpgrade(string upgradeId)
    {
        if (!CanPurchaseUpgrade(upgradeId)) return false;
        
        UpgradeItem upgrade = GetUpgrade(upgradeId);
        int cost = GetUpgradeCost(upgradeId);
        int scrapCost = GetUpgradeScrapCost(upgradeId);
        
        // Deduct costs
        playerCredits -= cost;
        playerScrap -= scrapCost;
        
        // Increase upgrade level
        upgradeLevels[upgradeId]++;
        
        // Apply upgrade effects
        ApplyUpgrade(upgrade);
        
        // Trigger events
        OnUpgradePurchased?.Invoke(upgrade);
        OnCreditsChanged?.Invoke(playerCredits);
        OnScrapChanged?.Invoke(playerScrap);
        
        if (AudioManager.Instance != null)
            AudioManager.Instance.PlaySFX("upgrade");
        
        Debug.Log($"Purchased upgrade: {upgrade.upgradeName} Level {GetUpgradeLevel(upgradeId)}");
        return true;
    }
    
    private void ApplyUpgrade(UpgradeItem upgrade)
    {
        // Apply upgrade effects based on type
        switch (upgrade.upgradeType)
        {
            case UpgradeType.Flashlight:
                ApplyFlashlightUpgrade(upgrade);
                break;
            case UpgradeType.Weapon:
                ApplyWeaponUpgrade(upgrade);
                break;
            case UpgradeType.Spacesuit:
                ApplySpacesuitUpgrade(upgrade);
                break;
            case UpgradeType.Spacecraft:
                ApplySpacecraftUpgrade(upgrade);
                break;
        }
    }
    
    private void ApplyFlashlightUpgrade(UpgradeItem upgrade)
    {
        // Find flashlight component and apply upgrades
        Flashlight flashlight = FindObjectOfType<Flashlight>();
        if (flashlight != null)
        {
            int level = GetUpgradeLevel(upgrade.upgradeId);
            
            switch (upgrade.upgradeId)
            {
                case "flashlight_brightness":
                    flashlight.SetBrightness(1f + (level * 0.3f));
                    break;
                case "flashlight_battery":
                    flashlight.SetBatteryLife(100f + (level * 50f));
                    break;
                case "flashlight_range":
                    flashlight.SetRange(10f + (level * 5f));
                    break;
            }
        }
    }
    
    private void ApplyWeaponUpgrade(UpgradeItem upgrade)
    {
        // Apply weapon upgrades to player's equipped weapons
        // This would integrate with your weapon system
        Debug.Log($"Applied weapon upgrade: {upgrade.upgradeId}");
    }
    
    private void ApplySpacesuitUpgrade(UpgradeItem upgrade)
    {
        PlayerResources playerResources = FindObjectOfType<PlayerResources>();
        if (playerResources != null)
        {
            int level = GetUpgradeLevel(upgrade.upgradeId);
            
            switch (upgrade.upgradeId)
            {
                case "suit_oxygen":
                    // Increase max oxygen
                    break;
                case "suit_protection":
                    // Increase damage resistance
                    break;
                case "suit_mobility":
                    // Increase movement speed
                    break;
            }
        }
    }
    
    private void ApplySpacecraftUpgrade(UpgradeItem upgrade)
    {
        // Apply spacecraft upgrades to player's ship
        Debug.Log($"Applied spacecraft upgrade: {upgrade.upgradeId}");
    }
    
    public void AddCredits(int amount)
    {
        playerCredits += amount;
        OnCreditsChanged?.Invoke(playerCredits);
    }
    
    public void AddScrap(int amount)
    {
        playerScrap += amount;
        OnScrapChanged?.Invoke(playerScrap);
    }
    
    public int GetUpgradeLevel(string upgradeId)
    {
        return upgradeLevels.ContainsKey(upgradeId) ? upgradeLevels[upgradeId] : 0;
    }
    
    public int GetUpgradeCost(string upgradeId)
    {
        UpgradeItem upgrade = GetUpgrade(upgradeId);
        if (upgrade == null) return 0;
        
        int currentLevel = GetUpgradeLevel(upgradeId);
        return Mathf.RoundToInt(upgrade.baseCost * Mathf.Pow(upgrade.costMultiplier, currentLevel));
    }
    
    public int GetUpgradeScrapCost(string upgradeId)
    {
        UpgradeItem upgrade = GetUpgrade(upgradeId);
        if (upgrade == null) return 0;
        
        int currentLevel = GetUpgradeLevel(upgradeId);
        return Mathf.RoundToInt(upgrade.baseScrapCost * Mathf.Pow(upgrade.costMultiplier, currentLevel));
    }
    
    public UpgradeItem GetUpgrade(string upgradeId)
    {
        foreach (var category in upgradeCategories)
        {
            var upgrade = category.upgrades.FirstOrDefault(u => u.upgradeId == upgradeId);
            if (upgrade != null) return upgrade;
        }
        return null;
    }
    
    public List<UpgradeCategory> GetAllCategories() => upgradeCategories;
}

[System.Serializable]
public class UpgradeCategory
{
    public string categoryName;
    public Sprite categoryIcon;
    public List<UpgradeItem> upgrades = new List<UpgradeItem>();
}

[System.Serializable]
public class UpgradeItem
{
    public string upgradeId;
    public string upgradeName;
    [TextArea(2, 4)]
    public string description;
    public int maxLevel = 5;
    public int baseCost = 100;
    public int baseScrapCost = 10;
    public float costMultiplier = 1.5f;
    public UpgradeType upgradeType;
    public Sprite upgradeIcon;
}

public enum UpgradeType
{
    Flashlight,
    Weapon,
    Spacesuit,
    Spacecraft,
    Equipment,
    Ship
}
