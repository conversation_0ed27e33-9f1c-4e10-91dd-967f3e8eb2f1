MultiIntegerSlotControlView {
    flex-direction: row;
    align-items: center;
}

#dummy > Label {
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 0;
    cursor: slide-arrow;
    -unity-text-align: middle-left;
}

.unity-base-field {
    width: 30px;
    margin-top: 1px;
    margin-bottom: 1px;
    -unity-text-align: middle-left;
}

.unity-base-field__input{
    margin-left: 0;
    margin-right: 0;
}

IntegerField.unity-integer-field {
    padding-left: 0;
    padding-right: 0;
}
