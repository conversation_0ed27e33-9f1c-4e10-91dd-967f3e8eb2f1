using UnityEngine;
using System.Collections;

[RequireComponent(typeof(CharacterController))]
public class PlayerController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float walkSpeed = 5f;
    public float sprintSpeed = 8f;
    public float crouchSpeed = 2.5f;
    public float jumpHeight = 2f;
    public float gravity = -9.81f;
    public float crouchHeight = 1f;
    public float standHeight = 2f;
    public float airControl = 0.3f;
    public float groundDrag = 5f;
    public float airDrag = 0f;

    [Header("Mouse Look")]
    public Camera playerCamera;
    public float mouseSensitivity = 2f;
    public float maxLookAngle = 90f;
    public Transform thirdPersonCameraPivot;
    public Camera thirdPersonCamera;
    public float thirdPersonDistance = 4f;
    public float cameraTransitionSpeed = 5f;

    [Header("Interaction")]
    public float interactionRange = 3f;
    public LayerMask interactionLayerMask = -1;
    public Transform interactionPoint;

    [Header("Audio")]
    public AudioClip[] footstepSounds;
    public float footstepInterval = 0.5f;
    public AudioClip jumpSound;
    public AudioClip landSound;

    [Header("Kenney Models")]
    public GameObject playerModelFP;
    public GameObject playerModelTP;
    public Animator playerAnimator;

    [Header("Effects")]
    public ParticleSystem dustParticles;
    public Transform dustSpawnPoint;

    // Private fields
    private CharacterController controller;
    private AudioSource audioSource;
    private Vector3 velocity;
    private Vector3 moveDirection;
    private bool isGrounded;
    private bool wasGrounded;
    private bool isCrouching = false;
    private bool isSprinting = false;
    private float xRotation = 0f;
    private bool isFirstPerson = true;
    private float footstepTimer = 0f;
    private IInteractable currentInteractable;
    private bool isInteracting = false;

    // Animation parameters
    private static readonly int Speed = Animator.StringToHash("Speed");
    private static readonly int IsGrounded = Animator.StringToHash("IsGrounded");
    private static readonly int IsCrouching = Animator.StringToHash("IsCrouching");
    private static readonly int IsJumping = Animator.StringToHash("IsJumping");

    void Start()
    {
        InitializePlayer();
    }

    void Update()
    {
        if (GameManager.Instance == null || GameManager.Instance.CurrentState != GameState.Playing)
            return;

        HandleInput();
        HandleMovement();
        HandleMouseLook();
        HandleInteraction();
        HandleAudio();
        UpdateAnimations();
    }

    private void InitializePlayer()
    {
        controller = GetComponent<CharacterController>();
        audioSource = GetComponent<AudioSource>();

        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();

        if (interactionPoint == null)
            interactionPoint = playerCamera.transform;

        SetCameraView(true);

        // Subscribe to events
        if (EventManager.Instance != null)
        {
            // Subscribe to relevant events here
        }
    }

    private void HandleInput()
    {
        // Camera switching
        if (Input.GetKeyDown(KeyCode.F4))
        {
            SetCameraView(!isFirstPerson);
        }

        // Interaction
        if (Input.GetKeyDown(KeyCode.E) && currentInteractable != null && !isInteracting)
        {
            StartCoroutine(InteractWithObject());
        }
    }

    void HandleMovement()
    {
        // Ground check
        wasGrounded = isGrounded;
        isGrounded = controller.isGrounded;

        // Reset falling velocity when grounded
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;

            // Landing effects
            if (!wasGrounded)
            {
                OnLanded();
            }
        }

        // Get input
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");

        // Calculate movement direction
        Vector3 inputDirection = new Vector3(horizontal, 0f, vertical).normalized;

        if (inputDirection.magnitude >= 0.1f)
        {
            // Calculate move direction relative to camera/player
            Vector3 forward = isFirstPerson ? transform.forward : thirdPersonCameraPivot.forward;
            Vector3 right = isFirstPerson ? transform.right : thirdPersonCameraPivot.right;

            // Remove Y component for movement
            forward.y = 0f;
            right.y = 0f;
            forward.Normalize();
            right.Normalize();

            moveDirection = (forward * inputDirection.z + right * inputDirection.x).normalized;
        }
        else
        {
            moveDirection = Vector3.zero;
        }

        // Calculate speed based on state
        float currentSpeed = walkSpeed;
        if (isCrouching)
            currentSpeed = crouchSpeed;
        else if (isSprinting && !isCrouching)
            currentSpeed = sprintSpeed;

        // Apply movement
        Vector3 move = moveDirection * currentSpeed;

        // Apply air control if not grounded
        if (!isGrounded)
            move *= airControl;

        controller.Move(move * Time.deltaTime);

        // Handle jumping
        if (Input.GetButtonDown("Jump") && isGrounded && !isCrouching)
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
            OnJumped();
        }

        // Handle crouching
        if (Input.GetKeyDown(KeyCode.LeftControl))
        {
            ToggleCrouch();
        }

        // Handle sprinting
        isSprinting = Input.GetKey(KeyCode.LeftShift) && moveDirection.magnitude > 0.1f && !isCrouching;

        // Apply gravity
        velocity.y += gravity * Time.deltaTime;
        controller.Move(velocity * Time.deltaTime);

        // Create dust particles when moving
        if (isGrounded && moveDirection.magnitude > 0.1f && dustParticles != null)
        {
            if (!dustParticles.isPlaying)
                dustParticles.Play();
        }
        else if (dustParticles != null && dustParticles.isPlaying)
        {
            dustParticles.Stop();
        }
    }

    void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;

        if (isFirstPerson)
        {
            // First-person camera control
            xRotation -= mouseY;
            xRotation = Mathf.Clamp(xRotation, -maxLookAngle, maxLookAngle);
            playerCamera.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
            transform.Rotate(Vector3.up * mouseX);
        }
        else
        {
            // Third-person camera orbit
            thirdPersonCameraPivot.Rotate(Vector3.up * mouseX, Space.World);
            thirdPersonCameraPivot.Rotate(Vector3.right * -mouseY, Space.Self);

            // Clamp vertical rotation
            Vector3 eulerAngles = thirdPersonCameraPivot.eulerAngles;
            if (eulerAngles.x > 180f) eulerAngles.x -= 360f;
            eulerAngles.x = Mathf.Clamp(eulerAngles.x, -maxLookAngle, maxLookAngle);
            thirdPersonCameraPivot.eulerAngles = eulerAngles;

            // Position camera
            Vector3 offset = -thirdPersonCameraPivot.forward * thirdPersonDistance;
            thirdPersonCamera.transform.position = thirdPersonCameraPivot.position + offset;
            thirdPersonCamera.transform.LookAt(thirdPersonCameraPivot);
        }
    }

    void HandleInteraction()
    {
        // Raycast for interactables
        Ray ray = new Ray(interactionPoint.position, interactionPoint.forward);
        RaycastHit hit;

        IInteractable newInteractable = null;

        if (Physics.Raycast(ray, out hit, interactionRange, interactionLayerMask))
        {
            newInteractable = hit.collider.GetComponent<IInteractable>();

            if (newInteractable != null && newInteractable.CanInteract)
            {
                // Check if this is a new interactable
                if (currentInteractable != newInteractable)
                {
                    // Exit previous interactable
                    if (currentInteractable != null)
                        currentInteractable.OnInteractionExit(gameObject);

                    // Enter new interactable
                    currentInteractable = newInteractable;
                    currentInteractable.OnInteractionEnter(gameObject);
                }
            }
            else
            {
                newInteractable = null;
            }
        }

        // If no valid interactable found, clear current
        if (newInteractable == null && currentInteractable != null)
        {
            currentInteractable.OnInteractionExit(gameObject);
            currentInteractable = null;
        }
    }

    private IEnumerator InteractWithObject()
    {
        if (currentInteractable == null) yield break;

        isInteracting = true;
        currentInteractable.Interact(gameObject);

        // Small delay to prevent rapid interactions
        yield return new WaitForSeconds(0.1f);

        isInteracting = false;
    }

    void HandleAudio()
    {
        // Handle footstep sounds
        if (isGrounded && moveDirection.magnitude > 0.1f)
        {
            footstepTimer += Time.deltaTime;

            float currentInterval = footstepInterval;
            if (isSprinting) currentInterval *= 0.7f;
            else if (isCrouching) currentInterval *= 1.5f;

            if (footstepTimer >= currentInterval)
            {
                PlayFootstepSound();
                footstepTimer = 0f;
            }
        }
        else
        {
            footstepTimer = 0f;
        }
    }

    void UpdateAnimations()
    {
        if (playerAnimator == null) return;

        // Calculate movement speed for animation
        float speed = moveDirection.magnitude;
        if (isSprinting) speed *= 1.5f;
        else if (isCrouching) speed *= 0.5f;

        playerAnimator.SetFloat(Speed, speed);
        playerAnimator.SetBool(IsGrounded, isGrounded);
        playerAnimator.SetBool(IsCrouching, isCrouching);
        playerAnimator.SetBool(IsJumping, !isGrounded && velocity.y > 0);
    }

    private void ToggleCrouch()
    {
        isCrouching = !isCrouching;

        // Smoothly adjust controller height
        StartCoroutine(AdjustControllerHeight());
    }

    private IEnumerator AdjustControllerHeight()
    {
        float targetHeight = isCrouching ? crouchHeight : standHeight;
        float startHeight = controller.height;
        float duration = 0.2f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            controller.height = Mathf.Lerp(startHeight, targetHeight, t);
            yield return null;
        }

        controller.height = targetHeight;
    }

    private void OnJumped()
    {
        if (jumpSound != null && audioSource != null)
            audioSource.PlayOneShot(jumpSound);

        if (AudioManager.Instance != null)
            AudioManager.Instance.PlaySFX("jump");
    }

    private void OnLanded()
    {
        if (landSound != null && audioSource != null)
            audioSource.PlayOneShot(landSound);

        if (AudioManager.Instance != null)
            AudioManager.Instance.PlaySFX("land");
    }

    private void PlayFootstepSound()
    {
        if (footstepSounds != null && footstepSounds.Length > 0 && audioSource != null)
        {
            AudioClip footstep = footstepSounds[Random.Range(0, footstepSounds.Length)];
            audioSource.PlayOneShot(footstep, 0.5f);
        }

        if (AudioManager.Instance != null)
            AudioManager.Instance.PlaySFX("footstep");
    }

    void SetCameraView(bool firstPerson)
    {
        isFirstPerson = firstPerson;

        // Smooth camera transition
        StartCoroutine(TransitionCamera(firstPerson));

        // Update models
        if (playerModelFP != null) playerModelFP.SetActive(firstPerson);
        if (playerModelTP != null) playerModelTP.SetActive(!firstPerson);
    }

    private IEnumerator TransitionCamera(bool toFirstPerson)
    {
        float duration = 1f / cameraTransitionSpeed;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            yield return null;
        }

        playerCamera.gameObject.SetActive(toFirstPerson);
        thirdPersonCamera.gameObject.SetActive(!toFirstPerson);
    }

    // Public getters for other systems
    public bool IsMoving => moveDirection.magnitude > 0.1f;
    public bool IsGrounded => isGrounded;
    public bool IsCrouching => isCrouching;
    public bool IsSprinting => isSprinting;
    public Vector3 MoveDirection => moveDirection;
    public IInteractable CurrentInteractable => currentInteractable;

    private void OnDestroy()
    {
        // Unsubscribe from events
        if (EventManager.Instance != null)
        {
            // Unsubscribe from relevant events here
        }
    }

    // Debug visualization
    private void OnDrawGizmosSelected()
    {
        if (interactionPoint != null)
        {
            Gizmos.color = currentInteractable != null ? Color.green : Color.red;
            Gizmos.DrawRay(interactionPoint.position, interactionPoint.forward * interactionRange);
        }
    }
}