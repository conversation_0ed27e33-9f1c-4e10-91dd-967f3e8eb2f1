using UnityEngine;

/// <summary>
/// Defines the types of items in the game
/// </summary>
public enum ItemType
{
    Consumable,
    Equipment,
    Weapon,
    Tool,
    Resource,
    KeyItem,
    Upgrade
}

/// <summary>
/// Defines equipment slots
/// </summary>
public enum EquipmentSlot
{
    None,
    Helmet,
    Suit,
    Gloves,
    Boots,
    Backpack,
    PrimaryWeapon,
    SecondaryWeapon,
    Tool
}

/// <summary>
/// Base class for all items in the game
/// </summary>
[CreateAssetMenu(fileName = "New Item", menuName = "Space Game/Item")]
public class Item : ScriptableObject
{
    [Header("Basic Info")]
    public string itemName = "New Item";
    public string description = "A mysterious item.";
    public Sprite icon;
    public GameObject worldModel;
    
    [Header("Item Properties")]
    public ItemType itemType = ItemType.Consumable;
    public EquipmentSlot equipmentSlot = EquipmentSlot.None;
    public int maxStackSize = 1;
    public float weight = 1f;
    public int value = 10;
    
    [Header("Consumable Properties")]
    public float healthRestore = 0f;
    public float hungerRestore = 0f;
    public float oxygenRestore = 0f;
    public float energyRestore = 0f;
    
    [Header("Equipment Properties")]
    public float durability = 100f;
    public float protection = 0f;
    public float oxygenCapacity = 0f;
    public float movementSpeedModifier = 0f;
    
    [Header("Audio")]
    public AudioClip useSound;
    public AudioClip equipSound;
    public AudioClip unequipSound;
    
    /// <summary>
    /// Use this item
    /// </summary>
    public virtual bool Use(GameObject user)
    {
        switch (itemType)
        {
            case ItemType.Consumable:
                return UseConsumable(user);
            case ItemType.Equipment:
                return EquipItem(user);
            default:
                Debug.Log($"Cannot use item of type {itemType}");
                return false;
        }
    }
    
    protected virtual bool UseConsumable(GameObject user)
    {
        PlayerResources playerResources = user.GetComponent<PlayerResources>();
        if (playerResources == null) return false;
        
        bool wasUsed = false;
        
        if (healthRestore > 0)
        {
            playerResources.Heal(healthRestore);
            wasUsed = true;
        }
        
        if (hungerRestore > 0)
        {
            playerResources.RestoreHunger(hungerRestore);
            wasUsed = true;
        }
        
        if (oxygenRestore > 0)
        {
            playerResources.RestoreOxygen(oxygenRestore);
            wasUsed = true;
        }
        
        if (energyRestore > 0)
        {
            playerResources.RestoreEnergy(energyRestore);
            wasUsed = true;
        }
        
        if (wasUsed)
        {
            PlayUseSound(user);
            
            // Trigger item used event
            if (EventManager.Instance != null)
            {
                EventManager.Instance.TriggerEvent(new ItemUsedEvent
                {
                    itemName = itemName,
                    quantity = 1
                });
            }
        }
        
        return wasUsed;
    }
    
    protected virtual bool EquipItem(GameObject user)
    {
        PlayerEquipment playerEquipment = user.GetComponent<PlayerEquipment>();
        if (playerEquipment == null) return false;
        
        return playerEquipment.EquipItem(this);
    }
    
    protected void PlayUseSound(GameObject user)
    {
        if (useSound != null)
        {
            AudioSource audioSource = user.GetComponent<AudioSource>();
            if (audioSource != null)
            {
                audioSource.PlayOneShot(useSound);
            }
        }
    }
    
    public virtual string GetTooltipText()
    {
        string tooltip = $"<b>{itemName}</b>\n{description}";
        
        if (itemType == ItemType.Consumable)
        {
            if (healthRestore > 0) tooltip += $"\n+{healthRestore} Health";
            if (hungerRestore > 0) tooltip += $"\n+{hungerRestore} Hunger";
            if (oxygenRestore > 0) tooltip += $"\n+{oxygenRestore} Oxygen";
            if (energyRestore > 0) tooltip += $"\n+{energyRestore} Energy";
        }
        else if (itemType == ItemType.Equipment)
        {
            if (protection > 0) tooltip += $"\n+{protection} Protection";
            if (oxygenCapacity > 0) tooltip += $"\n+{oxygenCapacity} Oxygen Capacity";
            if (movementSpeedModifier != 0) tooltip += $"\n{(movementSpeedModifier > 0 ? "+" : "")}{movementSpeedModifier * 100:F0}% Movement Speed";
        }
        
        tooltip += $"\nWeight: {weight}kg";
        tooltip += $"\nValue: {value} credits";
        
        return tooltip;
    }
}

/// <summary>
/// Represents an item instance in the inventory
/// </summary>
[System.Serializable]
public class ItemStack
{
    public Item item;
    public int quantity;
    public float durability;
    
    public ItemStack(Item item, int quantity = 1)
    {
        this.item = item;
        this.quantity = quantity;
        this.durability = item.durability;
    }
    
    public bool CanStackWith(Item otherItem)
    {
        return item == otherItem && quantity < item.maxStackSize;
    }
    
    public int AddToStack(int amount)
    {
        int canAdd = Mathf.Min(amount, item.maxStackSize - quantity);
        quantity += canAdd;
        return amount - canAdd; // Return remaining amount that couldn't be added
    }
    
    public bool IsEmpty => quantity <= 0;
    public bool IsFull => quantity >= item.maxStackSize;
    public float TotalWeight => item.weight * quantity;
}

/// <summary>
/// Component for items that can be picked up in the world
/// </summary>
public class WorldItem : InteractableBase
{
    [Header("World Item")]
    public Item item;
    public int quantity = 1;
    public bool autoPickup = false;
    public float autoPickupRange = 1.5f;

    [Header("Visual Effects")]
    public float bobHeight = 0.5f;
    public float bobSpeed = 2f;
    public bool rotateItem = true;
    public float rotationSpeed = 45f;

    private Vector3 startPosition;
    private PlayerInventory nearbyPlayer;

    protected override void Awake()
    {
        base.Awake();
        startPosition = transform.position;

        if (item != null)
        {
            interactionText = $"Pick up {item.itemName}";
        }
    }

    private void Update()
    {
        // Animate the item
        if (bobHeight > 0)
        {
            float newY = startPosition.y + Mathf.Sin(Time.time * bobSpeed) * bobHeight;
            transform.position = new Vector3(transform.position.x, newY, transform.position.z);
        }

        if (rotateItem)
        {
            transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
        }

        // Auto pickup check
        if (autoPickup && nearbyPlayer != null)
        {
            float distance = Vector3.Distance(transform.position, nearbyPlayer.transform.position);
            if (distance <= autoPickupRange)
            {
                TryPickup(nearbyPlayer.gameObject);
            }
        }
    }

    public override void Interact(GameObject player)
    {
        TryPickup(player);
    }

    public override void OnInteractionEnter(GameObject player)
    {
        base.OnInteractionEnter(player);
        nearbyPlayer = player.GetComponent<PlayerInventory>();
    }

    public override void OnInteractionExit(GameObject player)
    {
        base.OnInteractionExit(player);
        if (nearbyPlayer != null && nearbyPlayer.gameObject == player)
            nearbyPlayer = null;
    }

    private void TryPickup(GameObject player)
    {
        PlayerInventory inventory = player.GetComponent<PlayerInventory>();
        if (inventory == null) return;

        if (inventory.AddItem(item, quantity))
        {
            PlayInteractionSound();

            // Trigger pickup event
            if (EventManager.Instance != null)
            {
                EventManager.Instance.TriggerEvent(new ItemPickedUpEvent
                {
                    item = gameObject,
                    itemName = item.itemName,
                    quantity = quantity
                });
            }

            Destroy(gameObject);
        }
        else
        {
            Debug.Log("Inventory is full!");
        }
    }

    public void SetItem(Item newItem, int newQuantity = 1)
    {
        item = newItem;
        quantity = newQuantity;

        if (item != null)
        {
            interactionText = $"Pick up {item.itemName}";

            // Update visual model if available
            if (item.worldModel != null)
            {
                // Remove existing model
                foreach (Transform child in transform)
                {
                    if (child.name.Contains("Model"))
                        Destroy(child.gameObject);
                }

                // Instantiate new model
                GameObject model = Instantiate(item.worldModel, transform);
                model.name = "Model";
            }
        }
    }
}
