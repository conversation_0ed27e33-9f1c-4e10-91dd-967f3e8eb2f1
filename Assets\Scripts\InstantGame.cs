using UnityEngine;

/// <summary>
/// DRAG THIS ONTO ANY OBJECT AND PRESS PLAY - INSTANT SPACE GAME!
/// </summary>
public class InstantGame : MonoBehaviour
{
    private void Start()
    {
        Debug.Log("Creating instant space game...");
        
        // Player
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.transform.position = Vector3.up;
        
        CharacterController cc = player.AddComponent<CharacterController>();
        player.AddComponent<SimpleMove>();
        
        // Camera
        GameObject cam = new GameObject("Camera");
        cam.transform.SetParent(player.transform);
        cam.transform.localPosition = new Vector3(0, 1.6f, 0);
        Camera camera = cam.AddComponent<Camera>();
        cam.AddComponent<AudioListener>();
        
        // Floor
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.transform.localScale = Vector3.one * 10;
        
        // Test cube
        GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
        cube.transform.position = new Vector3(3, 0.5f, 0);
        cube.GetComponent<Renderer>().material.color = Color.red;
        
        Debug.Log("DONE! Use WASD to move, mouse to look around!");
    }
}

public class SimpleMove : MonoBehaviour
{
    private CharacterController controller;
    private Camera cam;
    private float xRot = 0;
    
    void Start()
    {
        controller = GetComponent<CharacterController>();
        cam = GetComponentInChildren<Camera>();
        Cursor.lockState = CursorLockMode.Locked;
    }
    
    void Update()
    {
        // Movement
        float h = Input.GetAxis("Horizontal");
        float v = Input.GetAxis("Vertical");
        Vector3 move = transform.right * h + transform.forward * v;
        controller.Move(move * 5 * Time.deltaTime);
        controller.Move(Vector3.down * 9.81f * Time.deltaTime);
        
        // Mouse look
        float mouseX = Input.GetAxis("Mouse X") * 2;
        float mouseY = Input.GetAxis("Mouse Y") * 2;
        
        xRot -= mouseY;
        xRot = Mathf.Clamp(xRot, -90, 90);
        
        cam.transform.localRotation = Quaternion.Euler(xRot, 0, 0);
        transform.Rotate(Vector3.up * mouseX);
    }
}
