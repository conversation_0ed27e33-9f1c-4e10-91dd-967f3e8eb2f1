using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Interactable computer terminal for space stations
/// </summary>
public class SpaceComputer : InteractableBase
{
    [Header("Computer Settings")]
    [SerializeField] private bool isPowered = true;
    [SerializeField] private bool requiresAccess = false;
    [SerializeField] private string accessCode = "1234";
    [SerializeField] private ComputerType computerType = ComputerType.General;
    
    [Header("Visual Effects")]
    [SerializeField] private Light screenLight;
    [SerializeField] private Color poweredColor = Color.cyan;
    [SerializeField] private Color unpoweredColor = Color.black;
    [SerializeField] private Renderer screenRenderer;
    [SerializeField] private Material poweredMaterial;
    [SerializeField] private Material unpoweredMaterial;
    
    [Header("Audio")]
    [SerializeField] private AudioClip computerBootSound;
    [SerializeField] private AudioClip computerBeepSound;
    [SerializeField] private AudioClip computerErrorSound;
    [SerializeField] private AudioClip computerShutdownSound;
    
    [Header("Computer Functions")]
    [SerializeField] private List<ComputerFunction> availableFunctions = new List<ComputerFunction>();
    
    private bool isAccessGranted = false;
    private ComputerUI computerUI;
    
    public enum ComputerType
    {
        General,
        Security,
        Engineering,
        Medical,
        Navigation,
        Communications,
        LifeSupport
    }
    
    [System.Serializable]
    public class ComputerFunction
    {
        public string functionName;
        public string description;
        public bool requiresSpecialAccess;
        public UnityEngine.Events.UnityEvent onExecute;
    }
    
    protected override void Awake()
    {
        base.Awake();
        
        computerUI = FindObjectOfType<ComputerUI>();
        UpdateVisuals();
        UpdateInteractionText();
    }
    
    private void Start()
    {
        // Subscribe to power events
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Subscribe<PowerSystemChangedEvent>(OnPowerChanged);
        }
    }
    
    public override void Interact(GameObject player)
    {
        if (!isPowered)
        {
            PlayErrorSound();
            Debug.Log("Computer has no power!");
            return;
        }
        
        if (requiresAccess && !isAccessGranted)
        {
            // Open access code input UI
            OpenAccessCodeInput(player);
            return;
        }
        
        // Open computer interface
        OpenComputerInterface(player);
    }
    
    public override void OnInteractionEnter(GameObject player)
    {
        base.OnInteractionEnter(player);
        UpdateInteractionText();
    }
    
    private void OpenAccessCodeInput(GameObject player)
    {
        // This would open a UI for entering access code
        // For now, just simulate access granted
        Debug.Log("Access code required. Enter code: " + accessCode);
        
        // In a real implementation, this would open a UI
        // For demo purposes, grant access immediately
        GrantAccess();
    }
    
    private void OpenComputerInterface(GameObject player)
    {
        PlayBeepSound();
        
        // Set game state to show computer UI
        if (GameManager.Instance != null)
        {
            GameManager.Instance.SetState(GameState.Inventory); // Reuse inventory state for UI
        }
        
        // Show computer UI
        if (computerUI != null)
        {
            computerUI.ShowComputer(this);
        }
        
        Debug.Log($"Opened {computerType} computer interface");
    }
    
    public void ExecuteFunction(string functionName)
    {
        ComputerFunction function = availableFunctions.Find(f => f.functionName == functionName);
        if (function != null)
        {
            if (function.requiresSpecialAccess && !isAccessGranted)
            {
                PlayErrorSound();
                Debug.Log("Access denied for function: " + functionName);
                return;
            }
            
            PlayBeepSound();
            function.onExecute?.Invoke();
            Debug.Log($"Executed function: {functionName}");
        }
        else
        {
            PlayErrorSound();
            Debug.Log("Function not found: " + functionName);
        }
    }
    
    public void GrantAccess()
    {
        isAccessGranted = true;
        PlayBeepSound();
        UpdateInteractionText();
        Debug.Log("Access granted to computer");
    }
    
    public void RevokeAccess()
    {
        isAccessGranted = false;
        UpdateInteractionText();
        Debug.Log("Access revoked from computer");
    }
    
    private void UpdateInteractionText()
    {
        if (!isPowered)
            interactionText = "Computer (No Power)";
        else if (requiresAccess && !isAccessGranted)
            interactionText = "Computer (Access Required)";
        else
            interactionText = $"Use {computerType} Computer";
    }
    
    private void UpdateVisuals()
    {
        // Update screen light
        if (screenLight != null)
        {
            screenLight.color = isPowered ? poweredColor : unpoweredColor;
            screenLight.enabled = isPowered;
        }
        
        // Update screen material
        if (screenRenderer != null)
        {
            if (isPowered && poweredMaterial != null)
                screenRenderer.material = poweredMaterial;
            else if (!isPowered && unpoweredMaterial != null)
                screenRenderer.material = unpoweredMaterial;
        }
    }
    
    private void PlayBeepSound()
    {
        if (computerBeepSound != null && audioSource != null)
            audioSource.PlayOneShot(computerBeepSound);
    }
    
    private void PlayErrorSound()
    {
        if (computerErrorSound != null && audioSource != null)
            audioSource.PlayOneShot(computerErrorSound);
    }
    
    private void OnPowerChanged(PowerSystemChangedEvent powerEvent)
    {
        SetPowered(powerEvent.isPowered);
    }
    
    public void SetPowered(bool powered)
    {
        bool wasUnpowered = !isPowered;
        isPowered = powered;
        
        if (powered && wasUnpowered)
        {
            // Computer booting up
            if (computerBootSound != null && audioSource != null)
                audioSource.PlayOneShot(computerBootSound);
        }
        else if (!powered && !wasUnpowered)
        {
            // Computer shutting down
            if (computerShutdownSound != null && audioSource != null)
                audioSource.PlayOneShot(computerShutdownSound);
                
            // Revoke access when power is lost
            isAccessGranted = false;
        }
        
        UpdateVisuals();
        UpdateInteractionText();
        canInteract = isPowered;
    }
    
    // Add predefined functions based on computer type
    private void AddDefaultFunctions()
    {
        availableFunctions.Clear();
        
        switch (computerType)
        {
            case ComputerType.Security:
                availableFunctions.Add(new ComputerFunction
                {
                    functionName = "View Security Cameras",
                    description = "Access security camera feeds",
                    requiresSpecialAccess = false
                });
                availableFunctions.Add(new ComputerFunction
                {
                    functionName = "Lock/Unlock Doors",
                    description = "Control door access",
                    requiresSpecialAccess = true
                });
                break;
                
            case ComputerType.Engineering:
                availableFunctions.Add(new ComputerFunction
                {
                    functionName = "Power Management",
                    description = "Control power distribution",
                    requiresSpecialAccess = true
                });
                availableFunctions.Add(new ComputerFunction
                {
                    functionName = "System Diagnostics",
                    description = "Run system diagnostics",
                    requiresSpecialAccess = false
                });
                break;
                
            case ComputerType.LifeSupport:
                availableFunctions.Add(new ComputerFunction
                {
                    functionName = "Oxygen Levels",
                    description = "Monitor oxygen levels",
                    requiresSpecialAccess = false
                });
                availableFunctions.Add(new ComputerFunction
                {
                    functionName = "Environmental Controls",
                    description = "Adjust environmental settings",
                    requiresSpecialAccess = true
                });
                break;
        }
    }
    
    // Properties
    public bool IsPowered => isPowered;
    public bool IsAccessGranted => isAccessGranted;
    public ComputerType Type => computerType;
    public List<ComputerFunction> Functions => availableFunctions;
    
    private void OnDestroy()
    {
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Unsubscribe<PowerSystemChangedEvent>(OnPowerChanged);
        }
    }
    
    private void OnValidate()
    {
        if (Application.isPlaying)
            AddDefaultFunctions();
    }
}

/// <summary>
/// Simple UI class for computer interface (placeholder)
/// </summary>
public class ComputerUI : MonoBehaviour
{
    public void ShowComputer(SpaceComputer computer)
    {
        // This would show the computer interface UI
        Debug.Log($"Showing {computer.Type} computer interface");
        
        // List available functions
        foreach (var function in computer.Functions)
        {
            Debug.Log($"- {function.functionName}: {function.description}");
        }
    }
    
    public void HideComputer()
    {
        // This would hide the computer interface UI
        Debug.Log("Hiding computer interface");
        
        if (GameManager.Instance != null)
        {
            GameManager.Instance.SetState(GameState.Playing);
        }
    }
}
