Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.11f1 (9b156bbbd4df) revision 10163563'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 32628 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-07-14T16:08:54Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.11f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/OneDrive/Desktop/space game
-logFile
Logs/AssetImportWorker0.log
-srvPort
52487
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/OneDrive/Desktop/space game
C:/Users/<USER>/OneDrive/Desktop/space game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [27760]  Target information:

Player connection [27760]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 497267771 [EditorId] 497267771 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [27760]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 497267771 [EditorId] 497267771 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [27760]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 497267771 [EditorId] 497267771 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [27760]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 497267771 [EditorId] 497267771 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [27760] Host joined multi-casting on [***********:54997]...
Player connection [27760] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.11f1 (9b156bbbd4df)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/OneDrive/Desktop/space game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 2060 SUPER (ID=0x1f06)
    Vendor:          NVIDIA
    VRAM:            8006 MB
    App VRAM Budget: 7238 MB
    Driver:          32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56624
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004579 seconds.
- Loaded All Assemblies, in  0.455 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.413 seconds
Domain Reload Profiling: 863ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (191ms)
		LoadAssemblies (144ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (185ms)
				TypeCache.ScanAssembly (167ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (413ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (363ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (62ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (148ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.935 seconds
Refreshing native plugins compatible for Editor in 2.12 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.019 seconds
Domain Reload Profiling: 1945ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (618ms)
		LoadAssemblies (442ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (218ms)
				TypeCache.ScanAssembly (200ms)
			BuildScriptInfoCaches (56ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1019ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (797ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (147ms)
			ProcessInitializeOnLoadAttributes (576ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 202 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (4.5 MB). Loaded Objects now: 6670.
Memory consumption went from 159.2 MB to 154.7 MB.
Total: 10.251700 ms (FindLiveObjects: 0.801100 ms CreateObjectMapping: 1.169300 ms MarkObjects: 5.289200 ms  DeleteObjects: 2.991100 ms)

========================================================================
Received Import Request.
  Time since last request: 3184.261540 seconds.
  path: Assets/InputSystem_Actions.inputactions
  artifactKey: Guid(052faaac586de48259a63d0c4782560b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem_Actions.inputactions using Guid(052faaac586de48259a63d0c4782560b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15be5ba4d5b2b3ffcc56475d7901d8b3') in 0.0409601 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 1335.194313 seconds.
  path: Packages/com.unity.ugui/package.json
  artifactKey: Guid(319b8889f363f5947acf209c17a94149) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.ugui/package.json using Guid(319b8889f363f5947acf209c17a94149) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'afcf94c3ba9d2d24c8a1da5eca5d4da6') in 0.0017833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 5.898941 seconds.
  path: Packages/com.unity.ugui/Editor/UGUI/UnityEditor.UI.asmdef
  artifactKey: Guid(343deaaf83e0cee4ca978e7df0b80d21) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.ugui/Editor/UGUI/UnityEditor.UI.asmdef using Guid(343deaaf83e0cee4ca978e7df0b80d21) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a6e05975fc44bdbfe9f60387ec160b2') in 0.0007003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.210 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.858 seconds
Domain Reload Profiling: 2059ms
	BeginReloadAssembly (342ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (133ms)
	RebuildNativeTypeToScriptingClass (96ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (581ms)
		LoadAssemblies (581ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (209ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (181ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (858ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (688ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 21 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (4.4 MB). Loaded Objects now: 6685.
Memory consumption went from 137.2 MB to 132.8 MB.
Total: 9.422200 ms (FindLiveObjects: 0.704200 ms CreateObjectMapping: 1.183400 ms MarkObjects: 4.954200 ms  DeleteObjects: 2.578900 ms)

Prepare: number of updated asset objects reloaded= 29
========================================================================
Received Prepare
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 12 Unused Serialized files (Serialized files now loaded: 0)
Unloading 108 unused Assets / (6.2 MB). Loaded Objects now: 6560.
Memory consumption went from 133.2 MB to 127.1 MB.
Total: 7.570200 ms (FindLiveObjects: 0.439100 ms CreateObjectMapping: 0.303700 ms MarkObjects: 5.271800 ms  DeleteObjects: 1.554100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (3.0 MB). Loaded Objects now: 6560.
Memory consumption went from 133.2 MB to 130.2 MB.
Total: 7.828500 ms (FindLiveObjects: 0.377000 ms CreateObjectMapping: 0.279000 ms MarkObjects: 6.408700 ms  DeleteObjects: 0.762500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.427 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.079 seconds
Domain Reload Profiling: 2498ms
	BeginReloadAssembly (498ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (113ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (791ms)
		LoadAssemblies (590ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (364ms)
			TypeCache.Refresh (196ms)
				TypeCache.ScanAssembly (178ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1080ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (890ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (601ms)
			ProcessInitializeOnLoadMethodAttributes (82ms)
			AfterProcessingInitializeOnLoad (17ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5988 unused Assets / (6.1 MB). Loaded Objects now: 6562.
Memory consumption went from 138.8 MB to 132.7 MB.
Total: 11.903800 ms (FindLiveObjects: 0.840800 ms CreateObjectMapping: 1.815500 ms MarkObjects: 5.960400 ms  DeleteObjects: 3.285800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2267.071763 seconds.
  path: Packages/com.unity.ai.navigation/package.json
  artifactKey: Guid(fbee67e09f48e4652bfa085460c895c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
Start importing Packages/com.unity.ai.navigation/package.json using Guid(fbee67e09f48e4652bfa085460c895c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a5fadf5674c76cf659010f7315ff0489') in 0.0363454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.350582 seconds.
  path: Packages/com.unity.ai.navigation/Editor/Unity.AI.Navigation.Editor.asmdef
  artifactKey: Guid(86c9d8e67265f41469be06142c397d17) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.ai.navigation/Editor/Unity.AI.Navigation.Editor.asmdef using Guid(86c9d8e67265f41469be06142c397d17) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e6f82ea8707f42273a4ca06b313e8fcf') in 0.000755 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

