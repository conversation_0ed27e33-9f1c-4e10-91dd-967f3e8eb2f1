using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Helper to create the game UI automatically
/// </summary>
public class UISetupHelper : MonoBehaviour
{
    [Header("UI Setup")]
    [SerializeField] private Canvas gameCanvas;
    
    [ContextMenu("Create Game UI")]
    public void CreateGameUI()
    {
        if (gameCanvas == null)
        {
            CreateCanvas();
        }
        
        CreateHUD();
        CreateCrosshair();
        CreateInteractionPanel();
        
        Debug.Log("Game UI created successfully!");
    }
    
    private void CreateCanvas()
    {
        GameObject canvasObj = new GameObject("GameCanvas");
        gameCanvas = canvasObj.AddComponent<Canvas>();
        gameCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        
        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        
        canvasObj.AddComponent<GraphicRaycaster>();
        
        Debug.Log("Created main canvas");
    }
    
    private void CreateHUD()
    {
        // Create HUD parent
        GameObject hudParent = new GameObject("HUD");
        hudParent.transform.SetParent(gameCanvas.transform, false);
        
        RectTransform hudRect = hudParent.AddComponent<RectTransform>();
        hudRect.anchorMin = Vector2.zero;
        hudRect.anchorMax = Vector2.one;
        hudRect.offsetMin = Vector2.zero;
        hudRect.offsetMax = Vector2.zero;
        
        // Create resource bars
        CreateResourceBars(hudParent);
        
        // Create quick slots
        CreateQuickSlots(hudParent);
        
        // Add GameHUD script
        hudParent.AddComponent<GameHUD>();
        
        Debug.Log("Created HUD");
    }
    
    private void CreateResourceBars(GameObject parent)
    {
        // Resource bars container
        GameObject barsContainer = new GameObject("ResourceBars");
        barsContainer.transform.SetParent(parent.transform, false);
        
        RectTransform barsRect = barsContainer.AddComponent<RectTransform>();
        barsRect.anchorMin = new Vector2(0, 1);
        barsRect.anchorMax = new Vector2(0, 1);
        barsRect.anchoredPosition = new Vector2(20, -20);
        barsRect.sizeDelta = new Vector2(300, 200);
        
        // Create individual resource bars
        CreateResourceBar(barsContainer, "Health", new Vector2(0, 0), Color.red);
        CreateResourceBar(barsContainer, "Hunger", new Vector2(0, -40), Color.orange);
        CreateResourceBar(barsContainer, "Oxygen", new Vector2(0, -80), Color.cyan);
        CreateResourceBar(barsContainer, "Energy", new Vector2(0, -120), Color.blue);
    }
    
    private void CreateResourceBar(GameObject parent, string resourceName, Vector2 position, Color color)
    {
        // Bar container
        GameObject barContainer = new GameObject($"{resourceName}Bar");
        barContainer.transform.SetParent(parent.transform, false);
        
        RectTransform containerRect = barContainer.AddComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(0, 1);
        containerRect.anchorMax = new Vector2(0, 1);
        containerRect.anchoredPosition = position;
        containerRect.sizeDelta = new Vector2(250, 30);
        
        // Background
        GameObject background = new GameObject("Background");
        background.transform.SetParent(barContainer.transform, false);
        
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        
        RectTransform bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;
        
        // Fill area
        GameObject fillArea = new GameObject("FillArea");
        fillArea.transform.SetParent(barContainer.transform, false);
        
        RectTransform fillAreaRect = fillArea.AddComponent<RectTransform>();
        fillAreaRect.anchorMin = Vector2.zero;
        fillAreaRect.anchorMax = Vector2.one;
        fillAreaRect.offsetMin = new Vector2(2, 2);
        fillAreaRect.offsetMax = new Vector2(-2, -2);
        
        // Fill
        GameObject fill = new GameObject("Fill");
        fill.transform.SetParent(fillArea.transform, false);
        
        Image fillImage = fill.AddComponent<Image>();
        fillImage.color = color;
        
        RectTransform fillRect = fill.GetComponent<RectTransform>();
        fillRect.anchorMin = Vector2.zero;
        fillRect.anchorMax = Vector2.one;
        fillRect.offsetMin = Vector2.zero;
        fillRect.offsetMax = Vector2.zero;
        
        // Slider component
        Slider slider = barContainer.AddComponent<Slider>();
        slider.fillRect = fillRect;
        slider.value = 1f;
        slider.interactable = false;
        
        // Label
        GameObject label = new GameObject("Label");
        label.transform.SetParent(barContainer.transform, false);
        
        TextMeshProUGUI labelText = label.AddComponent<TextMeshProUGUI>();
        labelText.text = resourceName;
        labelText.fontSize = 14;
        labelText.color = Color.white;
        labelText.alignment = TextAlignmentOptions.MiddleLeft;
        
        RectTransform labelRect = label.GetComponent<RectTransform>();
        labelRect.anchorMin = new Vector2(0, 0);
        labelRect.anchorMax = new Vector2(1, 1);
        labelRect.offsetMin = new Vector2(5, 0);
        labelRect.offsetMax = new Vector2(-5, 0);
    }
    
    private void CreateQuickSlots(GameObject parent)
    {
        // Quick slots container
        GameObject slotsContainer = new GameObject("QuickSlots");
        slotsContainer.transform.SetParent(parent.transform, false);
        
        RectTransform slotsRect = slotsContainer.AddComponent<RectTransform>();
        slotsRect.anchorMin = new Vector2(0.5f, 0);
        slotsRect.anchorMax = new Vector2(0.5f, 0);
        slotsRect.anchoredPosition = new Vector2(0, 50);
        slotsRect.sizeDelta = new Vector2(360, 60);
        
        // Horizontal layout
        HorizontalLayoutGroup layout = slotsContainer.AddComponent<HorizontalLayoutGroup>();
        layout.spacing = 5;
        layout.childControlWidth = false;
        layout.childControlHeight = false;
        
        // Create 6 quick slots
        for (int i = 0; i < 6; i++)
        {
            CreateQuickSlot(slotsContainer, i + 1);
        }
    }
    
    private void CreateQuickSlot(GameObject parent, int slotNumber)
    {
        GameObject slot = new GameObject($"QuickSlot{slotNumber}");
        slot.transform.SetParent(parent.transform, false);
        
        RectTransform slotRect = slot.AddComponent<RectTransform>();
        slotRect.sizeDelta = new Vector2(50, 50);
        
        // Background
        Image slotImage = slot.AddComponent<Image>();
        slotImage.color = new Color(0.3f, 0.3f, 0.3f, 0.8f);
        
        // Key number
        GameObject keyLabel = new GameObject("KeyLabel");
        keyLabel.transform.SetParent(slot.transform, false);
        
        TextMeshProUGUI keyText = keyLabel.AddComponent<TextMeshProUGUI>();
        keyText.text = slotNumber.ToString();
        keyText.fontSize = 12;
        keyText.color = Color.white;
        keyText.alignment = TextAlignmentOptions.BottomRight;
        
        RectTransform keyRect = keyLabel.GetComponent<RectTransform>();
        keyRect.anchorMin = Vector2.zero;
        keyRect.anchorMax = Vector2.one;
        keyRect.offsetMin = Vector2.zero;
        keyRect.offsetMax = Vector2.zero;
        
        // Item icon placeholder
        GameObject icon = new GameObject("Icon");
        icon.transform.SetParent(slot.transform, false);
        
        Image iconImage = icon.AddComponent<Image>();
        iconImage.color = new Color(1, 1, 1, 0); // Transparent initially
        
        RectTransform iconRect = icon.GetComponent<RectTransform>();
        iconRect.anchorMin = Vector2.zero;
        iconRect.anchorMax = Vector2.one;
        iconRect.offsetMin = new Vector2(5, 5);
        iconRect.offsetMax = new Vector2(-5, -5);
        
        // Add QuickSlotUI component
        slot.AddComponent<QuickSlotUI>();
    }
    
    private void CreateCrosshair()
    {
        GameObject crosshair = new GameObject("Crosshair");
        crosshair.transform.SetParent(gameCanvas.transform, false);
        
        RectTransform crosshairRect = crosshair.AddComponent<RectTransform>();
        crosshairRect.anchorMin = new Vector2(0.5f, 0.5f);
        crosshairRect.anchorMax = new Vector2(0.5f, 0.5f);
        crosshairRect.anchoredPosition = Vector2.zero;
        crosshairRect.sizeDelta = new Vector2(20, 20);
        
        Image crosshairImage = crosshair.AddComponent<Image>();
        crosshairImage.color = Color.white;
        
        // Create simple crosshair texture
        Texture2D crosshairTexture = new Texture2D(20, 20);
        Color[] pixels = new Color[400];
        
        // Fill with transparent
        for (int i = 0; i < pixels.Length; i++)
            pixels[i] = Color.clear;
        
        // Draw crosshair lines
        for (int i = 0; i < 20; i++)
        {
            pixels[i * 20 + 10] = Color.white; // Vertical line
            pixels[10 * 20 + i] = Color.white; // Horizontal line
        }
        
        crosshairTexture.SetPixels(pixels);
        crosshairTexture.Apply();
        
        Sprite crosshairSprite = Sprite.Create(crosshairTexture, new Rect(0, 0, 20, 20), new Vector2(0.5f, 0.5f));
        crosshairImage.sprite = crosshairSprite;
    }
    
    private void CreateInteractionPanel()
    {
        GameObject interactionPanel = new GameObject("InteractionPanel");
        interactionPanel.transform.SetParent(gameCanvas.transform, false);
        
        RectTransform panelRect = interactionPanel.AddComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.5f, 0.5f);
        panelRect.anchorMax = new Vector2(0.5f, 0.5f);
        panelRect.anchoredPosition = new Vector2(0, -100);
        panelRect.sizeDelta = new Vector2(300, 50);
        
        // Background
        Image panelImage = interactionPanel.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.7f);
        
        // Text
        GameObject text = new GameObject("InteractionText");
        text.transform.SetParent(interactionPanel.transform, false);
        
        TextMeshProUGUI interactionText = text.AddComponent<TextMeshProUGUI>();
        interactionText.text = "[E] Interact";
        interactionText.fontSize = 16;
        interactionText.color = Color.white;
        interactionText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = text.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        // Initially hidden
        interactionPanel.SetActive(false);
    }
}
