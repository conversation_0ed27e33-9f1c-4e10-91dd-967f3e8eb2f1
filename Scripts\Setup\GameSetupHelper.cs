using UnityEngine;

/// <summary>
/// Helper script to quickly set up the basic game scene
/// </summary>
public class GameSetupHelper : MonoBehaviour
{
    [Header("Setup Options")]
    [SerializeField] private bool createPlayer = true;
    [SerializeField] private bool createGameManager = true;
    [SerializeField] private bool createAudioManager = true;
    [SerializeField] private bool createEventManager = true;
    [SerializeField] private bool createMissionSystem = true;
    [SerializeField] private bool createBasicEnvironment = true;
    
    [Header("Player Setup")]
    [SerializeField] private Vector3 playerSpawnPosition = Vector3.zero;
    
    [Header("Environment")]
    [SerializeField] private Material floorMaterial;
    [SerializeField] private Material wallMaterial;
    
    [ContextMenu("Setup Game Scene")]
    public void SetupGameScene()
    {
        Debug.Log("Setting up Space Game scene...");
        
        if (createGameManager) CreateGameManager();
        if (createEventManager) CreateEventManager();
        if (createAudioManager) CreateAudioManager();
        if (createMissionSystem) CreateMissionSystem();
        if (createPlayer) CreatePlayer();
        if (createBasicEnvironment) CreateBasicEnvironment();
        
        Debug.Log("Space Game scene setup complete!");
    }
    
    private void CreateGameManager()
    {
        GameObject existing = GameObject.Find("GameManager");
        if (existing != null)
        {
            Debug.Log("GameManager already exists");
            return;
        }
        
        GameObject gameManager = new GameObject("GameManager");
        gameManager.AddComponent<GameManager>();
        Debug.Log("Created GameManager");
    }
    
    private void CreateEventManager()
    {
        GameObject existing = GameObject.Find("EventManager");
        if (existing != null)
        {
            Debug.Log("EventManager already exists");
            return;
        }
        
        GameObject eventManager = new GameObject("EventManager");
        eventManager.AddComponent<EventManager>();
        Debug.Log("Created EventManager");
    }
    
    private void CreateAudioManager()
    {
        GameObject existing = GameObject.Find("AudioManager");
        if (existing != null)
        {
            Debug.Log("AudioManager already exists");
            return;
        }
        
        GameObject audioManager = new GameObject("AudioManager");
        audioManager.AddComponent<AudioManager>();
        Debug.Log("Created AudioManager");
    }
    
    private void CreateMissionSystem()
    {
        GameObject existing = GameObject.Find("MissionSystem");
        if (existing != null)
        {
            Debug.Log("MissionSystem already exists");
            return;
        }
        
        GameObject missionSystem = new GameObject("MissionSystem");
        missionSystem.AddComponent<MissionSystem>();
        Debug.Log("Created MissionSystem");
    }
    
    private void CreatePlayer()
    {
        GameObject existing = GameObject.FindGameObjectWithTag("Player");
        if (existing != null)
        {
            Debug.Log("Player already exists");
            return;
        }
        
        // Create player GameObject
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.transform.position = playerSpawnPosition;
        
        // Add CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        controller.center = new Vector3(0, 1, 0);
        
        // Add player scripts
        player.AddComponent<PlayerController>();
        player.AddComponent<PlayerResources>();
        player.AddComponent<PlayerInventory>();
        player.AddComponent<PlayerEquipment>();
        player.AddComponent<AudioSource>();
        
        // Create camera setup
        CreatePlayerCameras(player);
        
        Debug.Log("Created Player with all components");
    }
    
    private void CreatePlayerCameras(GameObject player)
    {
        // First Person Camera
        GameObject fpCamera = new GameObject("FirstPersonCamera");
        fpCamera.transform.SetParent(player.transform);
        fpCamera.transform.localPosition = new Vector3(0, 1.6f, 0);
        
        Camera fpCam = fpCamera.AddComponent<Camera>();
        fpCam.fieldOfView = 75f;
        fpCamera.AddComponent<AudioListener>();
        
        // Third Person Camera Setup
        GameObject tpPivot = new GameObject("ThirdPersonCameraPivot");
        tpPivot.transform.SetParent(player.transform);
        tpPivot.transform.localPosition = new Vector3(0, 1.6f, 0);
        
        GameObject tpCamera = new GameObject("ThirdPersonCamera");
        tpCamera.transform.SetParent(tpPivot.transform);
        tpCamera.transform.localPosition = new Vector3(0, 0, -4);
        
        Camera tpCam = tpCamera.AddComponent<Camera>();
        tpCam.fieldOfView = 60f;
        tpCamera.SetActive(false);
        
        // Set references in PlayerController
        PlayerController playerController = player.GetComponent<PlayerController>();
        if (playerController != null)
        {
            // Use reflection to set private fields (for demo purposes)
            var fpCameraField = typeof(PlayerController).GetField("playerCamera", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            var tpPivotField = typeof(PlayerController).GetField("thirdPersonCameraPivot", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            var tpCameraField = typeof(PlayerController).GetField("thirdPersonCamera", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            
            if (fpCameraField != null) fpCameraField.SetValue(playerController, fpCam);
            if (tpPivotField != null) tpPivotField.SetValue(playerController, tpPivot.transform);
            if (tpCameraField != null) tpCameraField.SetValue(playerController, tpCam);
        }
    }
    
    private void CreateBasicEnvironment()
    {
        // Create floor
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.name = "Floor";
        floor.transform.localScale = new Vector3(10, 1, 10);
        floor.transform.position = new Vector3(0, -0.5f, 0);
        
        if (floorMaterial != null)
            floor.GetComponent<Renderer>().material = floorMaterial;
        
        // Create some walls
        CreateWall("Wall_North", new Vector3(0, 2, 10), new Vector3(20, 4, 1));
        CreateWall("Wall_South", new Vector3(0, 2, -10), new Vector3(20, 4, 1));
        CreateWall("Wall_East", new Vector3(10, 2, 0), new Vector3(1, 4, 20));
        CreateWall("Wall_West", new Vector3(-10, 2, 0), new Vector3(1, 4, 20));
        
        // Create basic lighting
        GameObject light = new GameObject("Directional Light");
        Light lightComp = light.AddComponent<Light>();
        lightComp.type = LightType.Directional;
        lightComp.intensity = 1f;
        light.transform.rotation = Quaternion.Euler(45, 45, 0);
        
        Debug.Log("Created basic environment");
    }
    
    private void CreateWall(string name, Vector3 position, Vector3 scale)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = name;
        wall.transform.position = position;
        wall.transform.localScale = scale;
        
        if (wallMaterial != null)
            wall.GetComponent<Renderer>().material = wallMaterial;
    }
    
    [ContextMenu("Create Sample Door")]
    public void CreateSampleDoor()
    {
        GameObject door = GameObject.CreatePrimitive(PrimitiveType.Cube);
        door.name = "SpaceDoor";
        door.transform.position = new Vector3(0, 1, 5);
        door.transform.localScale = new Vector3(2, 3, 0.2f);
        
        // Add door script
        door.AddComponent<SpaceDoor>();
        
        // Make it a trigger for interaction
        door.GetComponent<Collider>().isTrigger = false;
        
        Debug.Log("Created sample space door");
    }
    
    [ContextMenu("Create Sample Computer")]
    public void CreateSampleComputer()
    {
        GameObject computer = GameObject.CreatePrimitive(PrimitiveType.Cube);
        computer.name = "SpaceComputer";
        computer.transform.position = new Vector3(3, 1, 0);
        computer.transform.localScale = new Vector3(1, 1, 0.5f);
        
        // Add computer script
        computer.AddComponent<SpaceComputer>();
        
        Debug.Log("Created sample space computer");
    }
    
    [ContextMenu("Create Sample Rover")]
    public void CreateSampleRover()
    {
        GameObject rover = GameObject.CreatePrimitive(PrimitiveType.Cube);
        rover.name = "SpaceRover";
        rover.transform.position = new Vector3(-5, 0.5f, 0);
        rover.transform.localScale = new Vector3(3, 1, 2);
        
        // Add rigidbody and rover script
        rover.AddComponent<Rigidbody>();
        rover.AddComponent<SpaceRover>();
        
        Debug.Log("Created sample space rover");
    }
}
