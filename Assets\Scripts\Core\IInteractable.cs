using UnityEngine;

/// <summary>
/// Interface for objects that can be interacted with by the player
/// </summary>
public interface IInteractable
{
    /// <summary>
    /// The display name shown in the interaction UI
    /// </summary>
    string InteractionText { get; }
    
    /// <summary>
    /// Whether this object can currently be interacted with
    /// </summary>
    bool CanInteract { get; }
    
    /// <summary>
    /// The maximum distance from which this object can be interacted with
    /// </summary>
    float InteractionRange { get; }
    
    /// <summary>
    /// Called when the player interacts with this object
    /// </summary>
    /// <param name="player">The player GameObject performing the interaction</param>
    void Interact(GameObject player);
    
    /// <summary>
    /// Called when the player starts looking at this interactable
    /// </summary>
    /// <param name="player">The player GameObject</param>
    void OnInteractionEnter(GameObject player);
    
    /// <summary>
    /// Called when the player stops looking at this interactable
    /// </summary>
    /// <param name="player">The player GameObject</param>
    void OnInteractionExit(GameObject player);
}

/// <summary>
/// Base class for interactable objects
/// </summary>
public abstract class InteractableBase : MonoBehaviour, IInteractable
{
    [Header("Interaction Settings")]
    [SerializeField] protected string interactionText = "Interact";
    [SerializeField] protected float interactionRange = 3f;
    [SerializeField] protected bool canInteract = true;
    [SerializeField] protected AudioClip interactionSound;
    
    public virtual string InteractionText => interactionText;
    public virtual bool CanInteract => canInteract;
    public virtual float InteractionRange => interactionRange;
    
    protected AudioSource audioSource;
    
    protected virtual void Awake()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
    }
    
    public abstract void Interact(GameObject player);
    
    public virtual void OnInteractionEnter(GameObject player)
    {
        // Override in derived classes for custom behavior
    }
    
    public virtual void OnInteractionExit(GameObject player)
    {
        // Override in derived classes for custom behavior
    }
    
    protected virtual void PlayInteractionSound()
    {
        if (interactionSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(interactionSound);
        }
    }
}
