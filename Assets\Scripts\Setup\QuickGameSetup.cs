using UnityEngine;

/// <summary>
/// Quick setup script to get the space game running immediately
/// </summary>
public class QuickGameSetup : MonoBehaviour
{
    [Head<PERSON>("Quick Setup")]
    [SerializeField] private bool setupOnStart = true;
    
    private void Start()
    {
        if (setupOnStart)
        {
            SetupGame();
        }
    }
    
    [ContextMenu("Setup Complete Game")]
    public void SetupGame()
    {
        Debug.Log("Setting up Space Game...");
        
        // Create core managers
        CreateGameManager();
        CreateEventManager();
        CreateAudioManager();
        CreateMissionSystem();
        
        // Create player
        CreatePlayer();
        
        // Create basic environment
        CreateBasicEnvironment();
        
        // Create UI
        CreateGameUI();
        
        Debug.Log("Space Game setup complete! Press Play to start!");
    }
    
    private void CreateGameManager()
    {
        if (FindObjectOfType<GameManager>() == null)
        {
            GameObject gm = new GameObject("GameManager");
            gm.AddComponent<GameManager>();
            Debug.Log("Created GameManager");
        }
    }
    
    private void CreateEventManager()
    {
        if (FindObjectOfType<EventManager>() == null)
        {
            GameObject em = new GameObject("EventManager");
            em.AddComponent<EventManager>();
            Debug.Log("Created EventManager");
        }
    }
    
    private void CreateAudioManager()
    {
        if (FindObjectOfType<AudioManager>() == null)
        {
            GameObject am = new GameObject("AudioManager");
            am.AddComponent<AudioManager>();
            Debug.Log("Created AudioManager");
        }
    }
    
    private void CreateMissionSystem()
    {
        if (FindObjectOfType<MissionSystem>() == null)
        {
            GameObject ms = new GameObject("MissionSystem");
            ms.AddComponent<MissionSystem>();
            Debug.Log("Created MissionSystem");
        }
    }
    
    private void CreatePlayer()
    {
        GameObject existingPlayer = GameObject.FindGameObjectWithTag("Player");
        if (existingPlayer != null)
        {
            Debug.Log("Player already exists");
            return;
        }
        
        // Create player
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.transform.position = Vector3.zero;
        
        // Add CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        controller.center = new Vector3(0, 1, 0);
        
        // Add player components
        player.AddComponent<PlayerController>();
        player.AddComponent<PlayerResources>();
        player.AddComponent<PlayerInventory>();
        player.AddComponent<PlayerEquipment>();
        player.AddComponent<AudioSource>();
        
        // Create cameras
        CreatePlayerCameras(player);
        
        Debug.Log("Created Player");
    }
    
    private void CreatePlayerCameras(GameObject player)
    {
        // First Person Camera
        GameObject fpCamera = new GameObject("FirstPersonCamera");
        fpCamera.transform.SetParent(player.transform);
        fpCamera.transform.localPosition = new Vector3(0, 1.6f, 0);
        
        Camera fpCam = fpCamera.AddComponent<Camera>();
        fpCam.fieldOfView = 75f;
        fpCamera.AddComponent<AudioListener>();
        
        // Third Person Setup
        GameObject tpPivot = new GameObject("ThirdPersonCameraPivot");
        tpPivot.transform.SetParent(player.transform);
        tpPivot.transform.localPosition = new Vector3(0, 1.6f, 0);
        
        GameObject tpCamera = new GameObject("ThirdPersonCamera");
        tpCamera.transform.SetParent(tpPivot.transform);
        tpCamera.transform.localPosition = new Vector3(0, 0, -4);
        
        Camera tpCam = tpCamera.AddComponent<Camera>();
        tpCam.fieldOfView = 60f;
        tpCamera.SetActive(false);
        
        // Set references in PlayerController
        PlayerController pc = player.GetComponent<PlayerController>();
        if (pc != null)
        {
            pc.playerCamera = fpCam;
            pc.thirdPersonCameraPivot = tpPivot.transform;
            pc.thirdPersonCamera = tpCam;
        }
    }
    
    private void CreateBasicEnvironment()
    {
        // Create floor
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.name = "Floor";
        floor.transform.localScale = new Vector3(10, 1, 10);
        floor.transform.position = new Vector3(0, -0.5f, 0);
        
        // Create walls
        CreateWall("Wall_North", new Vector3(0, 2, 10), new Vector3(20, 4, 1));
        CreateWall("Wall_South", new Vector3(0, 2, -10), new Vector3(20, 4, 1));
        CreateWall("Wall_East", new Vector3(10, 2, 0), new Vector3(1, 4, 20));
        CreateWall("Wall_West", new Vector3(-10, 2, 0), new Vector3(1, 4, 20));
        
        // Create lighting
        GameObject light = new GameObject("Directional Light");
        Light lightComp = light.AddComponent<Light>();
        lightComp.type = LightType.Directional;
        lightComp.intensity = 1f;
        light.transform.rotation = Quaternion.Euler(45, 45, 0);
        
        // Create sample door
        CreateSampleDoor();
        
        Debug.Log("Created basic environment");
    }
    
    private void CreateWall(string name, Vector3 position, Vector3 scale)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = name;
        wall.transform.position = position;
        wall.transform.localScale = scale;
    }
    
    private void CreateSampleDoor()
    {
        GameObject door = GameObject.CreatePrimitive(PrimitiveType.Cube);
        door.name = "SpaceDoor";
        door.transform.position = new Vector3(0, 1.5f, 5);
        door.transform.localScale = new Vector3(2, 3, 0.2f);
        
        // Add door script
        SpaceDoor doorScript = door.AddComponent<SpaceDoor>();
        
        // Set door panel reference
        doorScript.GetType().GetField("doorPanel", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.SetValue(doorScript, door.transform);
        
        Debug.Log("Created sample door - press E to interact");
    }
    
    private void CreateGameUI()
    {
        // Create Canvas
        GameObject canvasObj = new GameObject("GameCanvas");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        
        canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
        canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
        
        // Create simple crosshair
        GameObject crosshair = new GameObject("Crosshair");
        crosshair.transform.SetParent(canvas.transform, false);
        
        UnityEngine.UI.Image crosshairImage = crosshair.AddComponent<UnityEngine.UI.Image>();
        crosshairImage.color = Color.white;
        
        RectTransform crosshairRect = crosshair.GetComponent<RectTransform>();
        crosshairRect.anchorMin = new Vector2(0.5f, 0.5f);
        crosshairRect.anchorMax = new Vector2(0.5f, 0.5f);
        crosshairRect.anchoredPosition = Vector2.zero;
        crosshairRect.sizeDelta = new Vector2(4, 20);
        
        // Create horizontal line
        GameObject crosshairH = new GameObject("CrosshairH");
        crosshairH.transform.SetParent(crosshair.transform, false);
        
        UnityEngine.UI.Image crosshairHImage = crosshairH.AddComponent<UnityEngine.UI.Image>();
        crosshairHImage.color = Color.white;
        
        RectTransform crosshairHRect = crosshairH.GetComponent<RectTransform>();
        crosshairHRect.anchorMin = new Vector2(0.5f, 0.5f);
        crosshairHRect.anchorMax = new Vector2(0.5f, 0.5f);
        crosshairHRect.anchoredPosition = Vector2.zero;
        crosshairHRect.sizeDelta = new Vector2(20, 4);
        
        // Create instruction text
        GameObject instructionText = new GameObject("Instructions");
        instructionText.transform.SetParent(canvas.transform, false);
        
        UnityEngine.UI.Text text = instructionText.AddComponent<UnityEngine.UI.Text>();
        text.text = "WASD: Move | Mouse: Look | F4: Switch Camera | E: Interact | Shift: Sprint | Ctrl: Crouch";
        text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        text.fontSize = 16;
        text.color = Color.white;
        text.alignment = TextAnchor.UpperLeft;
        
        RectTransform textRect = instructionText.GetComponent<RectTransform>();
        textRect.anchorMin = new Vector2(0, 1);
        textRect.anchorMax = new Vector2(1, 1);
        textRect.anchoredPosition = new Vector2(0, -20);
        textRect.sizeDelta = new Vector2(0, 30);
        
        Debug.Log("Created basic UI");
    }
}
