using UnityEngine;
using System.Collections;

/// <summary>
/// Base class for all vehicles in the space game
/// </summary>
public abstract class VehicleBase : InteractableBase
{
    [Header("Vehicle Settings")]
    [SerializeField] protected float maxSpeed = 10f;
    [SerializeField] protected float acceleration = 5f;
    [SerializeField] protected float deceleration = 8f;
    [SerializeField] protected float turnSpeed = 90f;
    [SerializeField] protected bool requiresFuel = true;
    [SerializeField] protected float maxFuel = 100f;
    [SerializeField] protected float currentFuel = 100f;
    [SerializeField] protected float fuelConsumption = 1f;
    
    [Header("Entry/Exit")]
    [SerializeField] protected Transform entryPoint;
    [SerializeField] protected Transform exitPoint;
    [SerializeField] protected Transform pilotSeat;
    [SerializeField] protected Camera vehicleCamera;
    
    [Header("Audio")]
    [SerializeField] protected AudioClip engineStartSound;
    [SerializeField] protected AudioClip engineLoopSound;
    [SerializeField] protected AudioClip engineStopSound;
    [SerializeField] protected AudioClip entrySound;
    [Serialize<PERSON>ield] protected AudioClip exitSound;
    
    [Header("Effects")]
    [SerializeField] protected ParticleSystem engineParticles;
    [SerializeField] protected Light[] vehicleLights;
    [SerializeField] protected Color poweredLightColor = Color.white;
    [SerializeField] protected Color unpoweredLightColor = Color.red;
    
    // Protected fields
    protected GameObject currentPilot;
    protected PlayerController playerController;
    protected Camera playerCamera;
    protected Rigidbody vehicleRigidbody;
    protected AudioSource engineAudioSource;
    protected bool isEngineRunning = false;
    protected bool isPiloted = false;
    protected float currentSpeed = 0f;
    protected Vector3 moveInput;
    
    // Properties
    public bool IsPiloted => isPiloted;
    public bool IsEngineRunning => isEngineRunning;
    public float FuelPercentage => currentFuel / maxFuel;
    public bool HasFuel => !requiresFuel || currentFuel > 0f;
    public GameObject CurrentPilot => currentPilot;
    
    protected override void Awake()
    {
        base.Awake();
        
        vehicleRigidbody = GetComponent<Rigidbody>();
        if (vehicleRigidbody == null)
            vehicleRigidbody = gameObject.AddComponent<Rigidbody>();
            
        // Set up engine audio source
        engineAudioSource = gameObject.AddComponent<AudioSource>();
        engineAudioSource.loop = true;
        engineAudioSource.playOnAwake = false;
        
        if (vehicleCamera != null)
            vehicleCamera.gameObject.SetActive(false);
            
        UpdateInteractionText();
        UpdateVehicleLights();
    }
    
    protected virtual void Update()
    {
        if (isPiloted)
        {
            HandlePilotInput();
            UpdateMovement();
            UpdateFuel();
            UpdateAudio();
        }
        
        UpdateEffects();
    }
    
    public override void Interact(GameObject player)
    {
        if (isPiloted)
        {
            ExitVehicle();
        }
        else
        {
            EnterVehicle(player);
        }
    }
    
    public override void OnInteractionEnter(GameObject player)
    {
        base.OnInteractionEnter(player);
        UpdateInteractionText();
    }
    
    protected virtual void EnterVehicle(GameObject player)
    {
        if (isPiloted) return;
        
        playerController = player.GetComponent<PlayerController>();
        if (playerController == null) return;
        
        // Store player camera
        playerCamera = playerController.playerCamera;
        
        // Position player at pilot seat
        if (pilotSeat != null)
        {
            player.transform.position = pilotSeat.position;
            player.transform.rotation = pilotSeat.rotation;
        }
        
        // Disable player controller
        playerController.enabled = false;
        
        // Hide player model
        if (playerController.playerModelFP != null)
            playerController.playerModelFP.SetActive(false);
        if (playerController.playerModelTP != null)
            playerController.playerModelTP.SetActive(false);
        
        // Enable vehicle camera
        if (vehicleCamera != null)
        {
            vehicleCamera.gameObject.SetActive(true);
            if (playerCamera != null)
                playerCamera.gameObject.SetActive(false);
        }
        
        // Set pilot
        currentPilot = player;
        isPiloted = true;
        
        // Play entry sound
        if (entrySound != null && audioSource != null)
            audioSource.PlayOneShot(entrySound);
        
        // Start engine
        StartEngine();
        
        UpdateInteractionText();
        
        // Trigger vehicle entered event
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new VehicleEnteredEvent
            {
                vehicle = gameObject,
                player = player
            });
        }
        
        Debug.Log($"Player entered {gameObject.name}");
    }
    
    protected virtual void ExitVehicle()
    {
        if (!isPiloted || currentPilot == null) return;
        
        // Stop engine
        StopEngine();
        
        // Position player at exit point
        if (exitPoint != null)
        {
            currentPilot.transform.position = exitPoint.position;
            currentPilot.transform.rotation = exitPoint.rotation;
        }
        
        // Re-enable player controller
        if (playerController != null)
        {
            playerController.enabled = true;
            
            // Show player model
            if (playerController.playerModelFP != null)
                playerController.playerModelFP.SetActive(true);
        }
        
        // Restore player camera
        if (playerCamera != null)
        {
            playerCamera.gameObject.SetActive(true);
            if (vehicleCamera != null)
                vehicleCamera.gameObject.SetActive(false);
        }
        
        // Play exit sound
        if (exitSound != null && audioSource != null)
            audioSource.PlayOneShot(exitSound);
        
        // Trigger vehicle exited event
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new VehicleExitedEvent
            {
                vehicle = gameObject,
                player = currentPilot
            });
        }
        
        Debug.Log($"Player exited {gameObject.name}");
        
        // Clear pilot
        GameObject exitedPlayer = currentPilot;
        currentPilot = null;
        playerController = null;
        playerCamera = null;
        isPiloted = false;
        
        UpdateInteractionText();
    }
    
    protected virtual void StartEngine()
    {
        if (isEngineRunning || !HasFuel) return;
        
        isEngineRunning = true;
        
        // Play engine start sound
        if (engineStartSound != null && audioSource != null)
            audioSource.PlayOneShot(engineStartSound);
        
        // Start engine loop sound
        if (engineLoopSound != null && engineAudioSource != null)
        {
            engineAudioSource.clip = engineLoopSound;
            engineAudioSource.Play();
        }
        
        // Start engine particles
        if (engineParticles != null)
            engineParticles.Play();
        
        UpdateVehicleLights();
    }
    
    protected virtual void StopEngine()
    {
        if (!isEngineRunning) return;
        
        isEngineRunning = false;
        
        // Play engine stop sound
        if (engineStopSound != null && audioSource != null)
            audioSource.PlayOneShot(engineStopSound);
        
        // Stop engine loop sound
        if (engineAudioSource != null)
            engineAudioSource.Stop();
        
        // Stop engine particles
        if (engineParticles != null)
            engineParticles.Stop();
        
        UpdateVehicleLights();
    }
    
    protected virtual void HandlePilotInput()
    {
        if (!isPiloted) return;
        
        // Get movement input
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        moveInput = new Vector3(horizontal, 0f, vertical);
        
        // Exit vehicle input
        if (Input.GetKeyDown(KeyCode.F))
        {
            ExitVehicle();
        }
        
        // Engine toggle
        if (Input.GetKeyDown(KeyCode.R))
        {
            if (isEngineRunning)
                StopEngine();
            else
                StartEngine();
        }
    }
    
    protected abstract void UpdateMovement();
    
    protected virtual void UpdateFuel()
    {
        if (!requiresFuel || !isEngineRunning) return;
        
        if (moveInput.magnitude > 0.1f)
        {
            currentFuel = Mathf.Max(0f, currentFuel - fuelConsumption * Time.deltaTime);
            
            if (currentFuel <= 0f)
            {
                StopEngine();
                Debug.Log("Vehicle out of fuel!");
            }
        }
    }
    
    protected virtual void UpdateAudio()
    {
        if (engineAudioSource != null && isEngineRunning)
        {
            // Adjust engine sound based on speed
            float speedRatio = currentSpeed / maxSpeed;
            engineAudioSource.pitch = Mathf.Lerp(0.8f, 1.5f, speedRatio);
            engineAudioSource.volume = Mathf.Lerp(0.3f, 1f, speedRatio);
        }
    }
    
    protected virtual void UpdateEffects()
    {
        // Update engine particles based on speed
        if (engineParticles != null && isEngineRunning)
        {
            var emission = engineParticles.emission;
            emission.rateOverTime = Mathf.Lerp(10f, 50f, currentSpeed / maxSpeed);
        }
    }
    
    protected virtual void UpdateInteractionText()
    {
        if (isPiloted)
            interactionText = "Exit Vehicle (F)";
        else if (!HasFuel)
            interactionText = "Vehicle (No Fuel)";
        else
            interactionText = "Enter Vehicle";
    }
    
    protected virtual void UpdateVehicleLights()
    {
        if (vehicleLights == null) return;
        
        Color lightColor = isEngineRunning ? poweredLightColor : unpoweredLightColor;
        
        foreach (Light light in vehicleLights)
        {
            if (light != null)
            {
                light.color = lightColor;
                light.enabled = isEngineRunning;
            }
        }
    }
    
    public virtual void AddFuel(float amount)
    {
        currentFuel = Mathf.Min(maxFuel, currentFuel + amount);
    }
    
    public virtual void SetFuel(float amount)
    {
        currentFuel = Mathf.Clamp(amount, 0f, maxFuel);
    }
    
    private void OnDrawGizmosSelected()
    {
        // Draw entry/exit points
        if (entryPoint != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(entryPoint.position, 0.5f);
            Gizmos.DrawLine(entryPoint.position, entryPoint.position + entryPoint.forward);
        }

        if (exitPoint != null)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(exitPoint.position, 0.5f);
            Gizmos.DrawLine(exitPoint.position, exitPoint.position + exitPoint.forward);
        }

        if (pilotSeat != null)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(pilotSeat.position, Vector3.one * 0.5f);
        }
    }
}

/// <summary>
/// Rover vehicle for ground exploration
/// </summary>
public class SpaceRover : VehicleBase
{
    [Header("Rover Settings")]
    [SerializeField] private float groundCheckDistance = 1.5f;
    [SerializeField] private LayerMask groundLayerMask = 1;
    [SerializeField] private float maxClimbAngle = 45f;
    [SerializeField] private Transform[] wheels;
    [SerializeField] private float wheelRotationSpeed = 360f;

    private bool isGrounded = true;

    protected override void UpdateMovement()
    {
        if (!isPiloted || !isEngineRunning || !HasFuel) return;

        // Check if grounded
        CheckGrounded();

        if (!isGrounded) return;

        // Calculate movement
        Vector3 moveDirection = transform.TransformDirection(moveInput);
        moveDirection.y = 0f; // Keep on ground

        // Apply acceleration/deceleration
        if (moveInput.magnitude > 0.1f)
        {
            currentSpeed = Mathf.MoveTowards(currentSpeed, maxSpeed * moveInput.magnitude, acceleration * Time.deltaTime);
        }
        else
        {
            currentSpeed = Mathf.MoveTowards(currentSpeed, 0f, deceleration * Time.deltaTime);
        }

        // Apply movement
        Vector3 velocity = moveDirection.normalized * currentSpeed;
        velocity.y = vehicleRigidbody.velocity.y; // Preserve vertical velocity
        vehicleRigidbody.velocity = velocity;

        // Apply rotation
        if (moveInput.magnitude > 0.1f)
        {
            float turnAmount = moveInput.x * turnSpeed * Time.deltaTime;
            transform.Rotate(0f, turnAmount, 0f);
        }

        // Rotate wheels
        RotateWheels();
    }

    private void CheckGrounded()
    {
        RaycastHit hit;
        isGrounded = Physics.Raycast(transform.position, Vector3.down, out hit, groundCheckDistance, groundLayerMask);

        if (isGrounded)
        {
            // Check slope angle
            float angle = Vector3.Angle(hit.normal, Vector3.up);
            if (angle > maxClimbAngle)
            {
                isGrounded = false;
            }
        }
    }

    private void RotateWheels()
    {
        if (wheels == null) return;

        float rotationAmount = currentSpeed * wheelRotationSpeed * Time.deltaTime;

        foreach (Transform wheel in wheels)
        {
            if (wheel != null)
            {
                wheel.Rotate(rotationAmount, 0f, 0f);
            }
        }
    }

    private void OnDrawGizmosSelected()
    {
        base.OnDrawGizmosSelected();

        // Draw ground check
        Gizmos.color = isGrounded ? Color.green : Color.red;
        Gizmos.DrawLine(transform.position, transform.position + Vector3.down * groundCheckDistance);
    }
}
