using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Manages ambient audio for different space environments
/// </summary>
public class SpaceAmbientManager : MonoBehaviour
{
    [Header("Ambient Zones")]
    [SerializeField] private List<AmbientZone> ambientZones = new List<AmbientZone>();
    [SerializeField] private AmbientZone defaultZone;
    
    [Header("Audio Settings")]
    [SerializeField] private float fadeSpeed = 2f;
    [SerializeField] private float maxDistance = 50f;
    [SerializeField] private AnimationCurve distanceFalloff = AnimationCurve.Linear(0, 1, 1, 0);
    
    private AmbientZone currentZone;
    private AudioSource ambientAudioSource;
    private Coroutine fadeCoroutine;
    private Transform player;
    
    [System.Serializable]
    public class AmbientZone
    {
        public string zoneName;
        public AudioClip ambientClip;
        public float volume = 0.5f;
        public bool loop = true;
        public float pitch = 1f;
        public AmbientType ambientType;
        
        [Header("3D Audio")]
        public bool is3D = false;
        public Transform sourcePosition;
        public float minDistance = 1f;
        public float maxDistance = 50f;
    }
    
    public enum AmbientType
    {
        SpaceStation,
        OuterSpace,
        EngineRoom,
        Hangar,
        Corridor,
        Bridge,
        MedicalBay,
        Airlock,
        PlanetSurface,
        Asteroid
    }
    
    private void Start()
    {
        InitializeAmbientManager();
    }
    
    private void Update()
    {
        UpdateAmbientAudio();
    }
    
    private void InitializeAmbientManager()
    {
        // Find player
        GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
        if (playerObj != null)
            player = playerObj.transform;
        
        // Create ambient audio source
        ambientAudioSource = gameObject.AddComponent<AudioSource>();
        ambientAudioSource.loop = true;
        ambientAudioSource.playOnAwake = false;
        ambientAudioSource.spatialBlend = 0f; // 2D by default
        
        // Set default zone
        if (defaultZone != null)
        {
            SetAmbientZone(defaultZone);
        }
    }
    
    private void UpdateAmbientAudio()
    {
        if (player == null || currentZone == null) return;
        
        // Update 3D audio if applicable
        if (currentZone.is3D && currentZone.sourcePosition != null)
        {
            float distance = Vector3.Distance(player.position, currentZone.sourcePosition.position);
            float volumeMultiplier = distanceFalloff.Evaluate(distance / currentZone.maxDistance);
            
            ambientAudioSource.volume = currentZone.volume * volumeMultiplier;
            
            // Update 3D audio settings
            ambientAudioSource.spatialBlend = 1f;
            ambientAudioSource.minDistance = currentZone.minDistance;
            ambientAudioSource.maxDistance = currentZone.maxDistance;
            ambientAudioSource.transform.position = currentZone.sourcePosition.position;
        }
        else
        {
            ambientAudioSource.spatialBlend = 0f; // 2D audio
        }
    }
    
    public void SetAmbientZone(AmbientZone zone)
    {
        if (zone == null || zone == currentZone) return;
        
        currentZone = zone;
        
        if (fadeCoroutine != null)
            StopCoroutine(fadeCoroutine);
            
        fadeCoroutine = StartCoroutine(FadeToAmbient(zone));
    }
    
    public void SetAmbientZone(AmbientType ambientType)
    {
        AmbientZone zone = ambientZones.Find(z => z.ambientType == ambientType);
        if (zone != null)
        {
            SetAmbientZone(zone);
        }
        else
        {
            Debug.LogWarning($"Ambient zone of type {ambientType} not found!");
        }
    }
    
    public void SetAmbientZone(string zoneName)
    {
        AmbientZone zone = ambientZones.Find(z => z.zoneName == zoneName);
        if (zone != null)
        {
            SetAmbientZone(zone);
        }
        else
        {
            Debug.LogWarning($"Ambient zone '{zoneName}' not found!");
        }
    }
    
    private IEnumerator FadeToAmbient(AmbientZone newZone)
    {
        // Fade out current audio
        float startVolume = ambientAudioSource.volume;
        
        while (ambientAudioSource.volume > 0f)
        {
            ambientAudioSource.volume -= startVolume * fadeSpeed * Time.deltaTime;
            yield return null;
        }
        
        // Change to new ambient
        if (newZone.ambientClip != null)
        {
            ambientAudioSource.clip = newZone.ambientClip;
            ambientAudioSource.loop = newZone.loop;
            ambientAudioSource.pitch = newZone.pitch;
            
            if (!ambientAudioSource.isPlaying)
                ambientAudioSource.Play();
        }
        else
        {
            ambientAudioSource.Stop();
        }
        
        // Fade in new audio
        float targetVolume = newZone.volume;
        
        while (ambientAudioSource.volume < targetVolume)
        {
            ambientAudioSource.volume += targetVolume * fadeSpeed * Time.deltaTime;
            yield return null;
        }
        
        ambientAudioSource.volume = targetVolume;
    }
    
    public void StopAmbient()
    {
        if (fadeCoroutine != null)
            StopCoroutine(fadeCoroutine);
            
        fadeCoroutine = StartCoroutine(FadeOutAmbient());
    }
    
    private IEnumerator FadeOutAmbient()
    {
        float startVolume = ambientAudioSource.volume;
        
        while (ambientAudioSource.volume > 0f)
        {
            ambientAudioSource.volume -= startVolume * fadeSpeed * Time.deltaTime;
            yield return null;
        }
        
        ambientAudioSource.Stop();
        currentZone = null;
    }
    
    public void SetVolume(float volume)
    {
        if (currentZone != null)
        {
            currentZone.volume = volume;
            if (!currentZone.is3D)
                ambientAudioSource.volume = volume;
        }
    }
    
    public void AddAmbientZone(AmbientZone zone)
    {
        if (!ambientZones.Contains(zone))
        {
            ambientZones.Add(zone);
        }
    }
    
    public void RemoveAmbientZone(AmbientZone zone)
    {
        ambientZones.Remove(zone);
    }
    
    // Properties
    public AmbientZone CurrentZone => currentZone;
    public bool IsPlaying => ambientAudioSource != null && ambientAudioSource.isPlaying;
    
    private void OnDrawGizmosSelected()
    {
        if (currentZone != null && currentZone.is3D && currentZone.sourcePosition != null)
        {
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(currentZone.sourcePosition.position, currentZone.minDistance);
            
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(currentZone.sourcePosition.position, currentZone.maxDistance);
        }
    }
}

/// <summary>
/// Trigger zone for changing ambient audio
/// </summary>
public class AmbientZoneTrigger : MonoBehaviour
{
    [Header("Zone Settings")]
    [SerializeField] private SpaceAmbientManager.AmbientType ambientType;
    [SerializeField] private string customZoneName;
    [SerializeField] private bool useCustomName = false;
    
    private SpaceAmbientManager ambientManager;
    
    private void Start()
    {
        ambientManager = FindObjectOfType<SpaceAmbientManager>();
        
        Collider col = GetComponent<Collider>();
        if (col != null)
            col.isTrigger = true;
    }
    
    private void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player") && ambientManager != null)
        {
            if (useCustomName && !string.IsNullOrEmpty(customZoneName))
            {
                ambientManager.SetAmbientZone(customZoneName);
            }
            else
            {
                ambientManager.SetAmbientZone(ambientType);
            }
            
            Debug.Log($"Player entered ambient zone: {(useCustomName ? customZoneName : ambientType.ToString())}");
        }
    }
    
    private void OnDrawGizmos()
    {
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            Gizmos.color = new Color(0f, 1f, 1f, 0.3f);
            
            if (col is BoxCollider)
            {
                BoxCollider boxCol = col as BoxCollider;
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawCube(boxCol.center, boxCol.size);
            }
            else if (col is SphereCollider)
            {
                SphereCollider sphereCol = col as SphereCollider;
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawSphere(sphereCol.center, sphereCol.radius);
            }
        }
    }
}
