GraphEditorView {
    flex-direction: column;
    flex: 1;
}

GraphEditorView #TitleBar {

}

GraphEditorView > #content {
    flex: 1;
    flex-direction: row;
}

GraphEditorView > #content > #GraphView {
    flex: 1;
}

GraphEditorView > #content > #inspector {
    width: 400px;
}

.edge.fromMatrix4, .edge.fromMatrix3, .edge.fromMatrix2 {
    --edge-output-color: #8FC1DF;
}
.edge.toMatrix4, .edge.toMatrix3, .edge.toMatrix2 {
    --edge-input-color: #8FC1DF;
}

.edge.fromTexture2D, .edge.fromCubemap {
    --edge-output-color: #FF8B8B;
}
.edge.toTexture2D, .edge.toCubemap {
    --edge-input-color: #FF8B8B;
}

.edge.fromVector4 {
    --edge-output-color: #FBCBF4;
}
.edge.toVector4 {
    --edge-input-color: #FBCBF4;
}

.edge.fromVector3 {
    --edge-output-color: #F6FF9A;
}
.edge.toVector3 {
    --edge-input-color: #F6FF9A;
}

.edge.fromVector2 {
    --edge-output-color: #9AEF92;
}
.edge.toVector2 {
    --edge-input-color: #9AEF92;
}

.edge.fromVector1 {
    --edge-output-color: #84E4E7;
}
.edge.toVector1 {
    --edge-input-color: #84E4E7;
}

.edge.fromBoolean {
    --edge-output-color: #9481E6;
}
.edge.toBoolean {
    --edge-input-color: #9481E6;
}

#resizeBorderFrame > .resize {
    background-color: rgba(0, 0, 0, 0);
    position: absolute;
}

#resizeBorderFrame > .resize.vertical {
    cursor: resize-vertical;
    height: 10px;
    left: 10px;
    right: 10px;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
}

#resizeBorderFrame > .resize.horizontal {
    cursor: resize-horizontal;
    width: 10px;
    top: 10px;
    bottom: 10px;
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
    margin-right: 0;
}

#resizeBorderFrame > .resize.diagonal {
    width: 10px;
    height: 6px;
}

#resizeBorderFrame > .resize.diagonal.top-left {
    cursor: resize-up-left;
    top: 0;
    left: 0;
}

#resizeBorderFrame > .resize.diagonal.top-right {
    cursor: resize-up-right;
    top: 0;
    right: 0;
}

#resizeBorderFrame > .resize.diagonal.bottom-left {
    cursor: resize-up-right;
    bottom: 0;
    left: 0;
}

#resizeBorderFrame > .resize.diagonal.bottom-right {
    cursor: resize-up-left;
    bottom: 0;
    right: 0;
}

#resizeBorderFrame > .resize.vertical.top {
    top: 0;
}

#resizeBorderFrame > .resize.vertical.bottom {
    bottom: 0;
}

#resizeBorderFrame > .resize.horzontal.left {
    left: 0;
}

#resizeBorderFrame > .resize.horizontal.right {
    right: 0;
}

.resizeBorderFrame {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.nodeEditor {
    border-color: rgb(79, 79, 79);
    border-bottom-width: 1px;
    padding-top: 10px;
}

NodeEditorHeaderView {
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 10px;
    flex-direction: row;
}

NodeEditorHeaderView > #preType {
    margin-left: 10px;
}

NodeEditorHeaderView > #preType,
NodeEditorHeaderView > #postType,
NodeEditorHeaderView > #type {
    color: rgb(180, 180, 180);
}

NodeEditorHeaderView > #title {
    color: rgb(180, 180, 180);
    -unity-font-style: bold;
}

.nodeEditor > .section {
    padding-bottom: 10px;
}

.nodeEditor > .section.hidden {
    height: 0;
    padding-bottom: 0;
}

.nodeEditor > .section > .title {
    color: rgb(180, 180, 180);
    -unity-font-style: bold;
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 2px;
}

.nodeEditor > .section > #slots {
    flex-direction: column;
    padding-left: 15px;
    padding-right: 15px;
}

.nodeEditor > .section#surfaceOptions {
    padding-left: 15px;
    padding-right: 15px;
}

IMGUISlotEditorView {
    flex-direction: column;
    padding-bottom: 2px;
}

ObjectControlView {
    flex-direction: row;
}

ObjectControlView > ObjectField {
    flex: 1;
}

PropertyControlView {
    padding-left: 8px;
    padding-right: 8px;
    padding-top: 4px;
    padding-bottom: 4px;
}

.stack-node {
    --separator-extent: 6;
}

/* TEMP STUFF THAT SHOULD ACTUALLY STAY IN GRAPHVIEW */

.unity-Doublefield-input {
    min-height: 15px;
    margin-left: 4px;
    margin-top: 2px;
    margin-right: 4px;
    margin-bottom: 2px;
    padding-left: 3px;
    padding-top: 1px;
    padding-right: 3px;
    padding-bottom: 2px;
    -unity-slice-left: 3;
    -unity-slice-top: 3;
    -unity-slice-right: 3;
    -unity-slice-bottom: 3;
    --unity-selection-color: rgba(61,128,223,166);
    cursor: text;
    color: #B4B4B4;
    background-image: resource("Builtin Skins/DarkSkin/Images/TextField.png");
    --unity-cursor-color:#B4B4B4;
}

.unity-Doublefield-input:focus {
    background-image: resource("Builtin Skins/DarkSkin/Images/TextField focused.png");
}
