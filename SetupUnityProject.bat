@echo off
echo Setting up Unity Space Game project...

REM Create Unity Assets folder structure
if not exist "Assets" mkdir "Assets"
if not exist "Assets\Scripts" mkdir "Assets\Scripts"
if not exist "Assets\Scripts\Core" mkdir "Assets\Scripts\Core"
if not exist "Assets\Scripts\Player" mkdir "Assets\Scripts\Player"
if not exist "Assets\Scripts\Inventory" mkdir "Assets\Scripts\Inventory"
if not exist "Assets\Scripts\Vehicles" mkdir "Assets\Scripts\Vehicles"
if not exist "Assets\Scripts\Environment" mkdir "Assets\Scripts\Environment"
if not exist "Assets\Scripts\Audio" mkdir "Assets\Scripts\Audio"
if not exist "Assets\Scripts\UI" mkdir "Assets\Scripts\UI"
if not exist "Assets\Scripts\Missions" mkdir "Assets\Scripts\Missions"
if not exist "Assets\Scripts\Setup" mkdir "Assets\Scripts\Setup"

REM Copy main scripts
if exist "Scripts\GameManager.cs" copy "Scripts\GameManager.cs" "Assets\Scripts\"
if exist "Scripts\PlayerController.cs" copy "Scripts\PlayerController.cs" "Assets\Scripts\"

REM Copy Core scripts
if exist "Scripts\Core\IInteractable.cs" copy "Scripts\Core\IInteractable.cs" "Assets\Scripts\Core\"
if exist "Scripts\Core\EventManager.cs" copy "Scripts\Core\EventManager.cs" "Assets\Scripts\Core\"
if exist "Scripts\Core\AudioManager.cs" copy "Scripts\Core\AudioManager.cs" "Assets\Scripts\Core\"

REM Copy Player scripts
if exist "Scripts\Player\PlayerResources.cs" copy "Scripts\Player\PlayerResources.cs" "Assets\Scripts\Player\"

REM Copy Inventory scripts
if exist "Scripts\Inventory\Item.cs" copy "Scripts\Inventory\Item.cs" "Assets\Scripts\Inventory\"
if exist "Scripts\Inventory\PlayerInventory.cs" copy "Scripts\Inventory\PlayerInventory.cs" "Assets\Scripts\Inventory\"
if exist "Scripts\Inventory\PlayerEquipment.cs" copy "Scripts\Inventory\PlayerEquipment.cs" "Assets\Scripts\Inventory\"

REM Copy Vehicle scripts
if exist "Scripts\Vehicles\VehicleBase.cs" copy "Scripts\Vehicles\VehicleBase.cs" "Assets\Scripts\Vehicles\"
if exist "Scripts\Vehicles\Spacecraft.cs" copy "Scripts\Vehicles\Spacecraft.cs" "Assets\Scripts\Vehicles\"

REM Copy Environment scripts
if exist "Scripts\Environment\OxygenZone.cs" copy "Scripts\Environment\OxygenZone.cs" "Assets\Scripts\Environment\"
if exist "Scripts\Environment\SpaceDoor.cs" copy "Scripts\Environment\SpaceDoor.cs" "Assets\Scripts\Environment\"
if exist "Scripts\Environment\SpaceComputer.cs" copy "Scripts\Environment\SpaceComputer.cs" "Assets\Scripts\Environment\"

REM Copy Audio scripts
if exist "Scripts\Audio\SpaceAmbientManager.cs" copy "Scripts\Audio\SpaceAmbientManager.cs" "Assets\Scripts\Audio\"

REM Copy UI scripts
if exist "Scripts\UI\GameHUD.cs" copy "Scripts\UI\GameHUD.cs" "Assets\Scripts\UI\"

REM Copy Mission scripts
if exist "Scripts\Missions\MissionSystem.cs" copy "Scripts\Missions\MissionSystem.cs" "Assets\Scripts\Missions\"
if exist "Scripts\Missions\MissionGiver.cs" copy "Scripts\Missions\MissionGiver.cs" "Assets\Scripts\Missions\"

REM Copy Setup scripts
if exist "Scripts\Setup\GameSetupHelper.cs" copy "Scripts\Setup\GameSetupHelper.cs" "Assets\Scripts\Setup\"
if exist "Scripts\Setup\UISetupHelper.cs" copy "Scripts\Setup\UISetupHelper.cs" "Assets\Scripts\Setup\"

echo.
echo ========================================
echo Unity Space Game Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Go back to Unity
echo 2. Press Ctrl+R to refresh assets
echo 3. Create empty GameObject, add GameSetupHelper
echo 4. Click "Setup Game Scene" in Inspector
echo 5. Create another GameObject, add UISetupHelper  
echo 6. Click "Create Game UI" in Inspector
echo 7. Press Play!
echo.
echo All scripts have been copied to Assets/Scripts/
echo ========================================
pause
