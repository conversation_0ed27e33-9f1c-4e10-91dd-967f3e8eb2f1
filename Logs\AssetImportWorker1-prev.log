Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.11f1 (9b156bbbd4df) revision 10163563'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 32628 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-07-14T16:08:54Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.11f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Users/<USER>/OneDrive/Desktop/space game
-logFile
Logs/AssetImportWorker1.log
-srvPort
52487
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/OneDrive/Desktop/space game
C:/Users/<USER>/OneDrive/Desktop/space game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [5236]  Target information:

Player connection [5236]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2292249266 [EditorId] 2292249266 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [5236]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 2292249266 [EditorId] 2292249266 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [5236]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 2292249266 [EditorId] 2292249266 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [5236]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2292249266 [EditorId] 2292249266 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [5236] Host joined multi-casting on [***********:54997]...
Player connection [5236] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.11f1 (9b156bbbd4df)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/OneDrive/Desktop/space game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 2060 SUPER (ID=0x1f06)
    Vendor:          NVIDIA
    VRAM:            8006 MB
    App VRAM Budget: 7238 MB
    Driver:          32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56908
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004808 seconds.
- Loaded All Assemblies, in  0.455 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.412 seconds
Domain Reload Profiling: 863ms
	BeginReloadAssembly (147ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (192ms)
		LoadAssemblies (143ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (189ms)
			TypeCache.Refresh (187ms)
				TypeCache.ScanAssembly (169ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (413ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (363ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (61ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (145ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.939 seconds
Refreshing native plugins compatible for Editor in 1.78 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.030 seconds
Domain Reload Profiling: 1961ms
	BeginReloadAssembly (204ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (436ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (218ms)
				TypeCache.ScanAssembly (198ms)
			BuildScriptInfoCaches (58ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1031ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (814ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (582ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 202 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (4.1 MB). Loaded Objects now: 6670.
Memory consumption went from 159.2 MB to 155.1 MB.
Total: 9.736000 ms (FindLiveObjects: 0.756100 ms CreateObjectMapping: 1.079400 ms MarkObjects: 5.344000 ms  DeleteObjects: 2.555400 ms)

========================================================================
Received Import Request.
  Time since last request: 3184.261714 seconds.
  path: Assets/Readme.asset
  artifactKey: Guid(8105016687592461f977c054a80ce2f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Readme.asset using Guid(8105016687592461f977c054a80ce2f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50ea5ad2cda8720781b90d71f311d8d1') in 0.0365948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.211 seconds
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.868 seconds
Domain Reload Profiling: 2071ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (169ms)
	RebuildNativeTypeToScriptingClass (65ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (591ms)
		LoadAssemblies (585ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (868ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (693ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (473ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 21 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (4.5 MB). Loaded Objects now: 6685.
Memory consumption went from 137.2 MB to 132.7 MB.
Total: 9.595100 ms (FindLiveObjects: 0.723100 ms CreateObjectMapping: 1.315400 ms MarkObjects: 4.896000 ms  DeleteObjects: 2.659200 ms)

Prepare: number of updated asset objects reloaded= 29
========================================================================
Received Prepare
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
Refreshing native plugins compatible for Editor in 1.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 12 Unused Serialized files (Serialized files now loaded: 0)
Unloading 108 unused Assets / (6.2 MB). Loaded Objects now: 6560.
Memory consumption went from 133.3 MB to 127.1 MB.
Total: 6.319300 ms (FindLiveObjects: 0.391300 ms CreateObjectMapping: 0.315000 ms MarkObjects: 4.210600 ms  DeleteObjects: 1.401400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (3.0 MB). Loaded Objects now: 6560.
Memory consumption went from 133.3 MB to 130.2 MB.
Total: 7.999200 ms (FindLiveObjects: 0.369000 ms CreateObjectMapping: 0.329300 ms MarkObjects: 6.477000 ms  DeleteObjects: 0.822300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Couldn't register non-exiting asset root folder: 'Assets'
AssetDatabase failed to register asset folder Assets
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.490 seconds
Refreshing native plugins compatible for Editor in 2.80 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.093 seconds
Domain Reload Profiling: 2576ms
	BeginReloadAssembly (534ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (53ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (808ms)
		LoadAssemblies (602ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (374ms)
			TypeCache.Refresh (191ms)
				TypeCache.ScanAssembly (176ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1094ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (898ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (620ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5988 unused Assets / (6.4 MB). Loaded Objects now: 6562.
Memory consumption went from 138.8 MB to 132.4 MB.
Total: 14.658100 ms (FindLiveObjects: 0.763600 ms CreateObjectMapping: 1.139400 ms MarkObjects: 7.912200 ms  DeleteObjects: 4.841300 ms)

Prepare: number of updated asset objects reloaded= 0
