using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Centralized event management system for the space game
/// </summary>
public class EventManager : MonoBehaviour
{
    public static EventManager Instance { get; private set; }
    
    private Dictionary<Type, List<Delegate>> eventDictionary = new Dictionary<Type, List<Delegate>>();
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    /// <summary>
    /// Subscribe to an event
    /// </summary>
    public void Subscribe<T>(Action<T> listener) where T : struct
    {
        Type eventType = typeof(T);
        
        if (!eventDictionary.ContainsKey(eventType))
        {
            eventDictionary[eventType] = new List<Delegate>();
        }
        
        eventDictionary[eventType].Add(listener);
    }
    
    /// <summary>
    /// Unsubscribe from an event
    /// </summary>
    public void Unsubscribe<T>(Action<T> listener) where T : struct
    {
        Type eventType = typeof(T);
        
        if (eventDictionary.ContainsKey(eventType))
        {
            eventDictionary[eventType].Remove(listener);
            
            if (eventDictionary[eventType].Count == 0)
            {
                eventDictionary.Remove(eventType);
            }
        }
    }
    
    /// <summary>
    /// Trigger an event
    /// </summary>
    public void TriggerEvent<T>(T eventData) where T : struct
    {
        Type eventType = typeof(T);
        
        if (eventDictionary.ContainsKey(eventType))
        {
            foreach (Delegate listener in eventDictionary[eventType])
            {
                try
                {
                    ((Action<T>)listener).Invoke(eventData);
                }
                catch (Exception e)
                {
                    Debug.LogError($"Error invoking event {eventType.Name}: {e.Message}");
                }
            }
        }
    }
    
    /// <summary>
    /// Clear all event subscriptions
    /// </summary>
    public void ClearAllEvents()
    {
        eventDictionary.Clear();
    }
    
    private void OnDestroy()
    {
        ClearAllEvents();
    }
}

// Game Events
public struct PlayerHealthChangedEvent
{
    public float currentHealth;
    public float maxHealth;
    public float previousHealth;
}

public struct PlayerHungerChangedEvent
{
    public float currentHunger;
    public float maxHunger;
    public float previousHunger;
}

public struct PlayerOxygenChangedEvent
{
    public float currentOxygen;
    public float maxOxygen;
    public float previousOxygen;
}

public struct ItemPickedUpEvent
{
    public GameObject item;
    public string itemName;
    public int quantity;
}

public struct ItemUsedEvent
{
    public string itemName;
    public int quantity;
}

public struct DoorOpenedEvent
{
    public GameObject door;
    public bool isOpen;
}

public struct VehicleEnteredEvent
{
    public GameObject vehicle;
    public GameObject player;
}

public struct VehicleExitedEvent
{
    public GameObject vehicle;
    public GameObject player;
}

public struct MissionCompletedEvent
{
    public string missionId;
    public string missionName;
    public int experienceGained;
}

public struct ObjectiveCompletedEvent
{
    public string objectiveId;
    public string missionId;
    public string description;
}

public struct PlayerLevelUpEvent
{
    public int newLevel;
    public int previousLevel;
    public int skillPoints;
}

public struct EnemyDefeatedEvent
{
    public GameObject enemy;
    public string enemyType;
    public int experienceGained;
}

public struct PowerSystemChangedEvent
{
    public float currentPower;
    public float maxPower;
    public bool isPowered;
}

public struct EnvironmentHazardEvent
{
    public Vector3 position;
    public string hazardType;
    public float damage;
}
