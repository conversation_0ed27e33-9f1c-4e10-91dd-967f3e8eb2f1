.node {
    min-height: 0px;
    width: 56px;
    border-radius: 8px;
    background-color: rgba(63, 63, 63, 1.0);
    padding: 0px;
    border-width: 0px;
}

#node-border {
    margin: 0px;
    border-radius: 8px;
    /* The proper opacity of the node border is 0.8 but transparent
    border are not working properly so it's fully opaque.*/
    border-color: rgba(25,25,25,1.0);
    border-width: 0px;
}

#selection-border {
    border-width: 2px;
    border-radius: 8px;
    margin: 0px;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

#title {
    flex-direction: row;
    justify-content: space-between;
    background-color: rgba(63,63,63,0.0);
    border-color: rgba(0,0,0,0);
    height: 0;
}

#input {
    width: 50%;
}

#output {
    width: 50%;
}

#contents > #top > #input {
    padding: 0px;
}

#contents > #top > #output {
    padding: 0px;
}

#divider.horizontal {
    width: 0.0px;
    border-right-width: 1px;
}

.port.input {
    width: 100%;
    padding: 0px;
}

.port.output {
    width: 100%;
    padding: 0px;
}

.port.input > #connector {
    margin-left: 6px;
    margin-right: 0px;
}

.port.output > #connector {
    margin-left: 0px;
    margin-right: 6px;
}

.port.input > #type {
    visibility: hidden;
    padding: 0px;
    margin: 0px;
    width: 0%;
    font-size: 1px;
    color: rgba(0,0,0,0);
}

.port.output > #type {
    visibility: hidden;
    padding: 0px;
    margin: 0px;
    width: 0%;
    font-size: 1px;
    color: rgba(0,0,0,0);
}
