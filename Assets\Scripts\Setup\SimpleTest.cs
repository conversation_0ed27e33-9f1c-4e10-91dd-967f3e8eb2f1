using UnityEngine;

/// <summary>
/// Simple test to see if auto-setup is working at all
/// </summary>
public class SimpleTest : MonoBehaviour
{
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    static void TestAutoSetup()
    {
        Debug.Log("=== AUTO-SETUP TEST RUNNING ===");
        
        // Create a simple cube to prove it's working
        GameObject testCube = GameObject.CreatePrimitive(PrimitiveType.Cube);
        testCube.name = "TEST_CUBE_AUTO_CREATED";
        testCube.transform.position = Vector3.zero;
        testCube.GetComponent<Renderer>().material.color = Color.red;
        
        Debug.Log("If you see a RED CUBE, auto-setup is working!");
        Debug.Log("If you don't see anything, there's a deeper Unity issue.");
        
        // Try to create a simple camera
        GameObject cameraObj = new GameObject("TestCamera");
        Camera cam = cameraObj.AddComponent<Camera>();
        cam.transform.position = new Vector3(0, 1, -5);
        cam.transform.LookAt(Vector3.zero);
        
        Debug.Log("Created test camera looking at red cube");
        
        // Add some lighting
        GameObject light = new GameObject("TestLight");
        Light lightComp = light.AddComponent<Light>();
        lightComp.type = LightType.Directional;
        lightComp.transform.rotation = Quaternion.Euler(45, 45, 0);
        
        Debug.Log("=== AUTO-SETUP TEST COMPLETE ===");
        Debug.Log("You should see: RED CUBE in center, Camera, and Light");
    }
}
