using UnityEngine;
using System.Collections;

/// <summary>
/// Manages player resources like health, hunger, oxygen, and energy
/// </summary>
public class PlayerResources : MonoBehaviour
{
    [Header("Health Settings")]
    [SerializeField] private float maxHealth = 100f;
    [SerializeField] private float currentHealth = 100f;
    [SerializeField] private float healthRegenRate = 1f;
    [SerializeField] private float healthRegenDelay = 5f;
    
    [Header("Hunger Settings")]
    [SerializeField] private float maxHunger = 100f;
    [SerializeField] private float currentHunger = 100f;
    [SerializeField] private float hungerDecayRate = 0.5f;
    [SerializeField] private float hungerDamageThreshold = 20f;
    [SerializeField] private float hungerDamageRate = 2f;
    
    [Header("Oxygen Settings")]
    [SerializeField] private float maxOxygen = 100f;
    [SerializeField] private float currentOxygen = 100f;
    [SerializeField] private float oxygenDecayRate = 1f;
    [SerializeField] private float oxygenDamageRate = 5f;
    [SerializeField] private bool isInOxygenZone = true;
    
    [Header("Energy Settings")]
    [SerializeField] private float maxEnergy = 100f;
    [SerializeField] private float currentEnergy = 100f;
    [SerializeField] private float energyRegenRate = 2f;
    [SerializeField] private float sprintEnergyDrain = 10f;
    [SerializeField] private float jumpEnergyDrain = 15f;
    
    [Header("Effects")]
    [SerializeField] private float lowHealthThreshold = 25f;
    [SerializeField] private float criticalHealthThreshold = 10f;
    [SerializeField] private AudioClip heartbeatSound;
    [SerializeField] private AudioClip breathingSound;
    [SerializeField] private AudioClip alarmSound;
    
    // Private fields
    private PlayerController playerController;
    private AudioSource audioSource;
    private Coroutine healthRegenCoroutine;
    private float lastDamageTime;
    private bool isRegeneratingHealth = false;
    
    // Properties
    public float HealthPercentage => currentHealth / maxHealth;
    public float HungerPercentage => currentHunger / maxHunger;
    public float OxygenPercentage => currentOxygen / maxOxygen;
    public float EnergyPercentage => currentEnergy / maxEnergy;
    
    public bool IsHealthCritical => currentHealth <= criticalHealthThreshold;
    public bool IsHealthLow => currentHealth <= lowHealthThreshold;
    public bool IsHungry => currentHunger <= hungerDamageThreshold;
    public bool IsOutOfOxygen => currentOxygen <= 0f;
    public bool IsExhausted => currentEnergy <= 10f;
    
    private void Start()
    {
        InitializeResources();
    }
    
    private void Update()
    {
        UpdateResources();
        HandleEffects();
    }
    
    private void InitializeResources()
    {
        playerController = GetComponent<PlayerController>();
        audioSource = GetComponent<AudioSource>();
        
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
            
        // Initialize resources to max
        currentHealth = maxHealth;
        currentHunger = maxHunger;
        currentOxygen = maxOxygen;
        currentEnergy = maxEnergy;
        
        lastDamageTime = Time.time;
    }
    
    private void UpdateResources()
    {
        float deltaTime = Time.deltaTime;
        
        // Update hunger
        UpdateHunger(deltaTime);
        
        // Update oxygen
        UpdateOxygen(deltaTime);
        
        // Update energy
        UpdateEnergy(deltaTime);
        
        // Update health regeneration
        UpdateHealthRegeneration();
    }
    
    private void UpdateHunger(float deltaTime)
    {
        // Decrease hunger over time
        currentHunger = Mathf.Max(0f, currentHunger - hungerDecayRate * deltaTime);
        
        // Damage from hunger
        if (currentHunger <= hungerDamageThreshold)
        {
            TakeDamage(hungerDamageRate * deltaTime, false);
        }
        
        // Trigger hunger event
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new PlayerHungerChangedEvent
            {
                currentHunger = currentHunger,
                maxHunger = maxHunger,
                previousHunger = currentHunger + hungerDecayRate * deltaTime
            });
        }
    }
    
    private void UpdateOxygen(float deltaTime)
    {
        if (!isInOxygenZone)
        {
            // Decrease oxygen when not in oxygen zone
            currentOxygen = Mathf.Max(0f, currentOxygen - oxygenDecayRate * deltaTime);
            
            // Damage from lack of oxygen
            if (currentOxygen <= 0f)
            {
                TakeDamage(oxygenDamageRate * deltaTime, false);
            }
        }
        else
        {
            // Regenerate oxygen when in oxygen zone
            currentOxygen = Mathf.Min(maxOxygen, currentOxygen + oxygenDecayRate * 2f * deltaTime);
        }
        
        // Trigger oxygen event
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new PlayerOxygenChangedEvent
            {
                currentOxygen = currentOxygen,
                maxOxygen = maxOxygen,
                previousOxygen = currentOxygen
            });
        }
    }
    
    private void UpdateEnergy(float deltaTime)
    {
        bool isMoving = playerController != null && playerController.IsMoving;
        bool isSprinting = playerController != null && playerController.IsSprinting;
        
        if (isSprinting && isMoving)
        {
            // Drain energy when sprinting
            currentEnergy = Mathf.Max(0f, currentEnergy - sprintEnergyDrain * deltaTime);
        }
        else if (!isMoving || currentEnergy < maxEnergy)
        {
            // Regenerate energy when not moving or not at max
            currentEnergy = Mathf.Min(maxEnergy, currentEnergy + energyRegenRate * deltaTime);
        }
    }
    
    private void UpdateHealthRegeneration()
    {
        // Start health regeneration if enough time has passed since last damage
        if (!isRegeneratingHealth && Time.time - lastDamageTime >= healthRegenDelay && currentHealth < maxHealth)
        {
            healthRegenCoroutine = StartCoroutine(RegenerateHealth());
        }
    }
    
    private IEnumerator RegenerateHealth()
    {
        isRegeneratingHealth = true;
        
        while (currentHealth < maxHealth && Time.time - lastDamageTime >= healthRegenDelay)
        {
            currentHealth = Mathf.Min(maxHealth, currentHealth + healthRegenRate * Time.deltaTime);
            
            // Trigger health event
            TriggerHealthChangedEvent();
            
            yield return null;
        }
        
        isRegeneratingHealth = false;
    }
    
    private void HandleEffects()
    {
        // Handle low health effects
        if (IsHealthCritical)
        {
            // Play heartbeat sound
            if (heartbeatSound != null && !audioSource.isPlaying)
            {
                audioSource.clip = heartbeatSound;
                audioSource.loop = true;
                audioSource.Play();
            }
        }
        else if (IsHealthLow)
        {
            // Slower heartbeat for low health
            if (heartbeatSound != null && (!audioSource.isPlaying || audioSource.clip != heartbeatSound))
            {
                audioSource.clip = heartbeatSound;
                audioSource.loop = true;
                audioSource.pitch = 0.8f;
                audioSource.Play();
            }
        }
        else
        {
            // Stop health effects
            if (audioSource.isPlaying && audioSource.clip == heartbeatSound)
            {
                audioSource.Stop();
                audioSource.pitch = 1f;
            }
        }
        
        // Handle oxygen effects
        if (IsOutOfOxygen)
        {
            if (breathingSound != null && (!audioSource.isPlaying || audioSource.clip != breathingSound))
            {
                audioSource.clip = breathingSound;
                audioSource.loop = true;
                audioSource.Play();
            }
        }
    }
    
    // Public methods for resource management
    public void TakeDamage(float damage, bool resetRegenTimer = true)
    {
        float previousHealth = currentHealth;
        currentHealth = Mathf.Max(0f, currentHealth - damage);
        
        if (resetRegenTimer)
        {
            lastDamageTime = Time.time;
            
            if (healthRegenCoroutine != null)
            {
                StopCoroutine(healthRegenCoroutine);
                isRegeneratingHealth = false;
            }
        }
        
        TriggerHealthChangedEvent(previousHealth);
        
        // Check for death
        if (currentHealth <= 0f)
        {
            Die();
        }
    }
    
    public void Heal(float amount)
    {
        float previousHealth = currentHealth;
        currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
        TriggerHealthChangedEvent(previousHealth);
    }
    
    public void RestoreHunger(float amount)
    {
        float previousHunger = currentHunger;
        currentHunger = Mathf.Min(maxHunger, currentHunger + amount);
        
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new PlayerHungerChangedEvent
            {
                currentHunger = currentHunger,
                maxHunger = maxHunger,
                previousHunger = previousHunger
            });
        }
    }
    
    public void RestoreOxygen(float amount)
    {
        currentOxygen = Mathf.Min(maxOxygen, currentOxygen + amount);
    }
    
    public void RestoreEnergy(float amount)
    {
        currentEnergy = Mathf.Min(maxEnergy, currentEnergy + amount);
    }
    
    public bool CanSprint()
    {
        return currentEnergy > 10f;
    }
    
    public bool CanJump()
    {
        return currentEnergy >= jumpEnergyDrain;
    }
    
    public void ConsumeEnergyForJump()
    {
        currentEnergy = Mathf.Max(0f, currentEnergy - jumpEnergyDrain);
    }
    
    public void SetOxygenZone(bool inOxygenZone)
    {
        isInOxygenZone = inOxygenZone;
    }
    
    private void TriggerHealthChangedEvent(float previousHealth = -1f)
    {
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new PlayerHealthChangedEvent
            {
                currentHealth = currentHealth,
                maxHealth = maxHealth,
                previousHealth = previousHealth >= 0 ? previousHealth : currentHealth
            });
        }
    }
    
    private void Die()
    {
        if (GameManager.Instance != null)
        {
            GameManager.Instance.GameOver();
        }
        
        Debug.Log("Player died!");
    }
}
