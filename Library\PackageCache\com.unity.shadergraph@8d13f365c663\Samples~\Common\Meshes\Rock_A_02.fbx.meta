fileFormatVersion: 2
guid: 0347a2ca593e212468d38a00c0f15925
AssetOrigin:
  serializedVersion: 1
  productId: 213197
  packageName: Unity Terrain - URP Demo Scene
  packageVersion: 1.0.2
  assetPath: Assets/TerrainDemoScene_URP/Prefabs/Rocks/Models/Rock_A_02.fbx
  uploadId: 531585
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: Rock_A_03_LOD00
  - first:
      1: 100004
    second: Rock_A_03_LOD01
  - first:
      1: 100006
    second: Rock_A_03_LOD02
  - first:
      1: 100008
    second: Rock_A_03_LOD03
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: Rock_A_03_LOD00
  - first:
      4: 400004
    second: Rock_A_03_LOD01
  - first:
      4: 400006
    second: Rock_A_03_LOD02
  - first:
      4: 400008
    second: Rock_A_03_LOD03
  - first:
      21: 2100000
    second: Rock_A
  - first:
      23: 2300000
    second: //RootNode
  - first:
      23: 2300002
    second: Rock_A_03_LOD00
  - first:
      23: 2300004
    second: Rock_A_03_LOD01
  - first:
      23: 2300006
    second: Rock_A_03_LOD02
  - first:
      23: 2300008
    second: Rock_A_03_LOD03
  - first:
      33: 3300000
    second: //RootNode
  - first:
      33: 3300002
    second: Rock_A_03_LOD00
  - first:
      33: 3300004
    second: Rock_A_03_LOD01
  - first:
      33: 3300006
    second: Rock_A_03_LOD02
  - first:
      33: 3300008
    second: Rock_A_03_LOD03
  - first:
      43: 4300000
    second: Rock_A_03
  - first:
      43: 4300002
    second: Rock_A_03_LOD00
  - first:
      43: 4300004
    second: Rock_A_03_LOD01
  - first:
      43: 4300006
    second: Rock_A_03_LOD02
  - first:
      43: 4300008
    second: Rock_A_03_LOD03
  - first:
      205: 20500000
    second: //RootNode
  - first:
      41386430: 2186277476908879412
    second: ImportLogs
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Rock_A
    second: {fileID: 2100000, guid: 2f23e371304cd884b86bfd3e58e4df77, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.0625
    - 0.01
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 0
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  importBlendShapeDeformPercent: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
