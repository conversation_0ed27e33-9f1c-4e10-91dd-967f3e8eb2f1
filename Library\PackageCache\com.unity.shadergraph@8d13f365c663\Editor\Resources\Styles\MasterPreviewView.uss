MasterPreviewView {
    flex-direction: column;
    position: absolute;
    right: 10px;
    bottom: 10px;
    width: 125px;
    height: 125px;
    min-width: 100px;
    min-height: 100px;
}

.mainContainer {
    margin: 6px;
    background-color: #2e2e2e;
    justify-content: flex-start;
    border-radius: 6px;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-color: rgb(25,25,25);
}

.mainContainer > #top {
    flex-direction: row;
    justify-content: space-between;
    background-color: #393939;
    padding: 8px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.mainContainer > #top > #title {
    overflow: hidden;
    font-size: 14px;
    color: rgb(180, 180, 180);
    padding: 1px 2px 2px;
}

.mainContainer > #middle {
    background-color: #2e2e2e;
    flex-grow: 1;
    flex-direction: row;
}

.mainContainer > #middle > #preview {
    flex-grow: 1;
    width: 100px;
    height: 100px;
}
