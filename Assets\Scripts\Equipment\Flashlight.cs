using UnityEngine;
using System.Collections;

/// <summary>
/// Upgradeable flashlight system for exploring dark ships
/// </summary>
public class Flashlight : MonoBehaviour
{
    [Header("Flashlight Settings")]
    [SerializeField] private Light flashlightLight;
    [SerializeField] private float baseBrightness = 2f;
    [SerializeField] private float baseRange = 10f;
    [SerializeField] private float baseBatteryLife = 100f;
    [SerializeField] private float batteryDrainRate = 1f;
    [SerializeField] private KeyCode toggleKey = KeyCode.F;
    
    [Header("Visual Effects")]
    [SerializeField] private GameObject flashlightModel;
    [SerializeField] private AudioClip flashlightOnSound;
    [SerializeField] private AudioClip flashlightOffSound;
    [SerializeField] private AudioClip batteryLowSound;
    [SerializeField] private ParticleSystem dustParticles;
    
    [Header("Battery Settings")]
    [SerializeField] private float lowBatteryThreshold = 20f;
    [SerializeField] private float criticalBatteryThreshold = 5f;
    [SerializeField] private float flickerIntensity = 0.5f;
    [SerializeField] private float flickerSpeed = 10f;
    
    // Private fields
    private AudioSource audioSource;
    private bool isOn = false;
    private float currentBattery;
    private float currentBrightness;
    private float currentRange;
    private float currentBatteryLife;
    private bool isFlickering = false;
    private Coroutine flickerCoroutine;
    private bool batteryLowSoundPlayed = false;
    
    // Properties
    public bool IsOn => isOn;
    public float BatteryPercentage => currentBattery / currentBatteryLife;
    public bool IsBatteryLow => currentBattery <= lowBatteryThreshold;
    public bool IsBatteryCritical => currentBattery <= criticalBatteryThreshold;
    
    // Events
    public System.Action<bool> OnFlashlightToggled;
    public System.Action<float> OnBatteryChanged;
    
    private void Start()
    {
        InitializeFlashlight();
    }
    
    private void Update()
    {
        HandleInput();
        UpdateBattery();
        UpdateFlashlightEffects();
    }
    
    private void InitializeFlashlight()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
        
        // Create flashlight light if not assigned
        if (flashlightLight == null)
        {
            GameObject lightObj = new GameObject("FlashlightLight");
            lightObj.transform.SetParent(transform);
            lightObj.transform.localPosition = Vector3.forward * 0.5f;
            
            flashlightLight = lightObj.AddComponent<Light>();
            flashlightLight.type = LightType.Spot;
            flashlightLight.spotAngle = 45f;
            flashlightLight.innerSpotAngle = 30f;
        }
        
        // Initialize values
        currentBrightness = baseBrightness;
        currentRange = baseRange;
        currentBatteryLife = baseBatteryLife;
        currentBattery = currentBatteryLife;
        
        // Apply initial settings
        UpdateFlashlightSettings();
        SetFlashlightState(false);
    }
    
    private void HandleInput()
    {
        if (Input.GetKeyDown(toggleKey))
        {
            ToggleFlashlight();
        }
    }
    
    private void UpdateBattery()
    {
        if (!isOn) return;
        
        // Drain battery
        currentBattery = Mathf.Max(0f, currentBattery - batteryDrainRate * Time.deltaTime);
        
        // Turn off if battery is dead
        if (currentBattery <= 0f && isOn)
        {
            SetFlashlightState(false);
            Debug.Log("Flashlight battery depleted!");
        }
        
        // Play low battery warning
        if (IsBatteryLow && !batteryLowSoundPlayed)
        {
            PlaySound(batteryLowSound);
            batteryLowSoundPlayed = true;
        }
        
        // Reset low battery sound when battery is recharged
        if (!IsBatteryLow)
        {
            batteryLowSoundPlayed = false;
        }
        
        // Trigger battery changed event
        OnBatteryChanged?.Invoke(BatteryPercentage);
    }
    
    private void UpdateFlashlightEffects()
    {
        if (!isOn) return;
        
        // Handle flickering for low battery
        if (IsBatteryCritical && !isFlickering)
        {
            StartFlickering();
        }
        else if (!IsBatteryCritical && isFlickering)
        {
            StopFlickering();
        }
        
        // Dim light based on battery level
        if (IsBatteryLow)
        {
            float dimFactor = Mathf.Lerp(0.3f, 1f, currentBattery / lowBatteryThreshold);
            flashlightLight.intensity = currentBrightness * dimFactor;
        }
        else
        {
            flashlightLight.intensity = currentBrightness;
        }
    }
    
    public void ToggleFlashlight()
    {
        if (currentBattery <= 0f && !isOn)
        {
            Debug.Log("Flashlight battery is dead!");
            return;
        }
        
        SetFlashlightState(!isOn);
    }
    
    public void SetFlashlightState(bool state)
    {
        isOn = state;
        flashlightLight.enabled = isOn;
        
        if (flashlightModel != null)
            flashlightModel.SetActive(isOn);
        
        // Play sound
        AudioClip soundToPlay = isOn ? flashlightOnSound : flashlightOffSound;
        PlaySound(soundToPlay);
        
        // Handle dust particles
        if (dustParticles != null)
        {
            if (isOn)
                dustParticles.Play();
            else
                dustParticles.Stop();
        }
        
        // Stop flickering when turned off
        if (!isOn && isFlickering)
        {
            StopFlickering();
        }
        
        OnFlashlightToggled?.Invoke(isOn);
        
        Debug.Log($"Flashlight {(isOn ? "ON" : "OFF")} - Battery: {BatteryPercentage:P0}");
    }
    
    private void StartFlickering()
    {
        if (flickerCoroutine != null)
            StopCoroutine(flickerCoroutine);
            
        isFlickering = true;
        flickerCoroutine = StartCoroutine(FlickerEffect());
    }
    
    private void StopFlickering()
    {
        if (flickerCoroutine != null)
        {
            StopCoroutine(flickerCoroutine);
            flickerCoroutine = null;
        }
        
        isFlickering = false;
        
        if (isOn)
            flashlightLight.intensity = currentBrightness;
    }
    
    private IEnumerator FlickerEffect()
    {
        while (isFlickering && isOn)
        {
            float flickerValue = Mathf.PerlinNoise(Time.time * flickerSpeed, 0f);
            float intensity = Mathf.Lerp(flickerIntensity, 1f, flickerValue) * currentBrightness;
            
            if (IsBatteryLow)
            {
                float dimFactor = Mathf.Lerp(0.3f, 1f, currentBattery / lowBatteryThreshold);
                intensity *= dimFactor;
            }
            
            flashlightLight.intensity = intensity;
            yield return null;
        }
    }
    
    private void PlaySound(AudioClip clip)
    {
        if (clip != null && audioSource != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    private void UpdateFlashlightSettings()
    {
        if (flashlightLight != null)
        {
            flashlightLight.intensity = currentBrightness;
            flashlightLight.range = currentRange;
        }
    }
    
    // Upgrade methods called by UpgradeSystem
    public void SetBrightness(float brightness)
    {
        currentBrightness = brightness;
        UpdateFlashlightSettings();
    }
    
    public void SetRange(float range)
    {
        currentRange = range;
        UpdateFlashlightSettings();
    }
    
    public void SetBatteryLife(float batteryLife)
    {
        float batteryRatio = BatteryPercentage;
        currentBatteryLife = batteryLife;
        currentBattery = currentBatteryLife * batteryRatio; // Maintain current percentage
    }
    
    public void RechargeBattery(float amount)
    {
        currentBattery = Mathf.Min(currentBatteryLife, currentBattery + amount);
        OnBatteryChanged?.Invoke(BatteryPercentage);
    }
    
    public void FullyChargeBattery()
    {
        currentBattery = currentBatteryLife;
        OnBatteryChanged?.Invoke(BatteryPercentage);
    }
    
    // Save/Load battery state
    public FlashlightSaveData GetSaveData()
    {
        return new FlashlightSaveData
        {
            currentBattery = this.currentBattery,
            currentBrightness = this.currentBrightness,
            currentRange = this.currentRange,
            currentBatteryLife = this.currentBatteryLife,
            isOn = this.isOn
        };
    }
    
    public void LoadSaveData(FlashlightSaveData saveData)
    {
        currentBattery = saveData.currentBattery;
        currentBrightness = saveData.currentBrightness;
        currentRange = saveData.currentRange;
        currentBatteryLife = saveData.currentBatteryLife;
        
        UpdateFlashlightSettings();
        SetFlashlightState(saveData.isOn);
    }
    
    private void OnDrawGizmosSelected()
    {
        if (flashlightLight != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(flashlightLight.transform.position, currentRange);
            
            // Draw light cone
            Vector3 forward = flashlightLight.transform.forward;
            float angle = flashlightLight.spotAngle * 0.5f * Mathf.Deg2Rad;
            float radius = Mathf.Tan(angle) * currentRange;
            
            Gizmos.color = new Color(1f, 1f, 0f, 0.3f);
            Gizmos.DrawLine(flashlightLight.transform.position, 
                flashlightLight.transform.position + forward * currentRange);
        }
    }
}

[System.Serializable]
public class FlashlightSaveData
{
    public float currentBattery;
    public float currentBrightness;
    public float currentRange;
    public float currentBatteryLife;
    public bool isOn;
}
