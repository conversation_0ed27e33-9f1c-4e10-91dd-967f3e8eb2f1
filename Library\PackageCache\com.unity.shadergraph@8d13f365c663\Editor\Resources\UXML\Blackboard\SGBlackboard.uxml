<UXML xmlns:ui="UnityEngine.UIElements" xmlns:sg="UnityEditor.ShaderGraph.Drawing">
    <ui:VisualElement name="content" pickingMode="Ignore">
        <ui:VisualElement name="header" pickingMode="Ignore">
            <ui:VisualElement name="labelContainer" pickingMode="Ignore">
                <ui:Label name="titleLabel" text="" />
                <ui:Label name="subTitleLabel" text="Blackboard">
                    <ui:TextField name="subTitleTextField" text="Blackboard" />
                </ui:Label>
            </ui:VisualElement>
            <ui:Button name="addButton" text="+" />
        </ui:VisualElement>
        <ui:VisualElement name="scrollBoundaryTop" pickingMode="Position" />
        <ui:ScrollView class="unity-scroll-view unity-scroll-view--scroll unity-scroll-view--vertical-horizontal" name="scrollView" mode="VerticalAndHorizontal"/>
        <ui:VisualElement name="contentContainer" pickingMode="Ignore" />
        <ui:VisualElement name="scrollBoundaryBottom" pickingMode="Position"/>
    </ui:VisualElement>
    <sg:ResizableElement pickingMode="Ignore" resizeRestriction="FlexDirection"/>
</UXML>
