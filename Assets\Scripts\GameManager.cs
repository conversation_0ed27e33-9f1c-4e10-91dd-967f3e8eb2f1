using UnityEngine;
using UnityEngine.SceneManagement;
using System;
using System.Collections;

public enum GameState { Menu, Playing, Paused, GameOver, Loading, Inventory }

public class GameManager : MonoBehaviour
{
    [Header("Game Settings")]
    public bool debugMode = false;
    public float autoSaveInterval = 300f; // 5 minutes

    [Header("Scene Management")]
    public string mainMenuScene = "MainMenu";
    public string gameScene = "SpaceStation";
    public string loadingScene = "Loading";

    // Events
    public static event Action<GameState> OnGameStateChanged;
    public static event Action OnGameSaved;
    public static event Action OnGameLoaded;

    // Properties
    public static GameManager Instance { get; private set; }
    public GameState CurrentState { get; private set; } = GameState.Menu;
    public GameState PreviousState { get; private set; }
    public bool IsGamePaused => CurrentState == GameState.Paused;
    public bool IsInGame => CurrentState == GameState.Playing;

    // Private fields
    private Coroutine autoSaveCoroutine;
    private float gameTime = 0f;

    private void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeGame();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        SetState(GameState.Menu);
    }

    private void Update()
    {
        if (IsInGame)
        {
            gameTime += Time.deltaTime;
            HandleGameInput();
        }
    }

    private void InitializeGame()
    {
        // Initialize core systems
        Application.targetFrameRate = 60;
        QualitySettings.vSyncCount = 1;

        // Set up auto-save
        if (autoSaveInterval > 0)
        {
            autoSaveCoroutine = StartCoroutine(AutoSaveRoutine());
        }
    }

    private void HandleGameInput()
    {
        // ESC key handling
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            if (CurrentState == GameState.Playing)
                PauseGame();
            else if (CurrentState == GameState.Paused)
                ResumeGame();
            else if (CurrentState == GameState.Inventory)
                CloseInventory();
        }

        // Tab key for inventory
        if (Input.GetKeyDown(KeyCode.Tab) && CurrentState == GameState.Playing)
        {
            OpenInventory();
        }
    }

    public void SetState(GameState newState)
    {
        if (CurrentState == newState) return;

        PreviousState = CurrentState;
        CurrentState = newState;

        // Handle time scale and cursor
        switch (newState)
        {
            case GameState.Menu:
                Time.timeScale = 1f;
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
                break;

            case GameState.Playing:
                Time.timeScale = 1f;
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
                break;

            case GameState.Paused:
            case GameState.GameOver:
            case GameState.Inventory:
                Time.timeScale = 0f;
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
                break;

            case GameState.Loading:
                Time.timeScale = 1f;
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
                break;
        }

        // Notify listeners
        OnGameStateChanged?.Invoke(newState);

        if (debugMode)
            Debug.Log($"Game state changed from {PreviousState} to {CurrentState}");
    }

    // Game Control Methods
    public void StartGame()
    {
        StartCoroutine(LoadGameScene());
    }

    public void PauseGame()
    {
        if (CurrentState == GameState.Playing)
            SetState(GameState.Paused);
    }

    public void ResumeGame()
    {
        if (CurrentState == GameState.Paused)
            SetState(GameState.Playing);
    }

    public void OpenInventory()
    {
        if (CurrentState == GameState.Playing)
            SetState(GameState.Inventory);
    }

    public void CloseInventory()
    {
        if (CurrentState == GameState.Inventory)
            SetState(GameState.Playing);
    }

    public void GameOver()
    {
        SetState(GameState.GameOver);
        SaveGame(); // Auto-save on game over
    }

    public void ReturnToMenu()
    {
        StartCoroutine(LoadMenuScene());
    }

    public void QuitGame()
    {
        SaveGame();
        Application.Quit();
    }

    // Scene Management
    private IEnumerator LoadGameScene()
    {
        SetState(GameState.Loading);

        AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(gameScene);
        asyncLoad.allowSceneActivation = false;

        while (!asyncLoad.isDone)
        {
            if (asyncLoad.progress >= 0.9f)
            {
                asyncLoad.allowSceneActivation = true;
            }
            yield return null;
        }

        SetState(GameState.Playing);
    }

    private IEnumerator LoadMenuScene()
    {
        SetState(GameState.Loading);

        AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(mainMenuScene);
        asyncLoad.allowSceneActivation = false;

        while (!asyncLoad.isDone)
        {
            if (asyncLoad.progress >= 0.9f)
            {
                asyncLoad.allowSceneActivation = true;
            }
            yield return null;
        }

        SetState(GameState.Menu);
    }

    // Save/Load System
    public void SaveGame()
    {
        try
        {
            GameSaveData saveData = new GameSaveData
            {
                gameTime = this.gameTime,
                currentScene = SceneManager.GetActiveScene().name,
                saveTimestamp = System.DateTime.Now.ToBinary()
            };

            string json = JsonUtility.ToJson(saveData, true);
            string savePath = Application.persistentDataPath + "/savegame.json";
            System.IO.File.WriteAllText(savePath, json);

            OnGameSaved?.Invoke();

            if (debugMode)
                Debug.Log($"Game saved to: {savePath}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to save game: {e.Message}");
        }
    }

    public void LoadGame()
    {
        try
        {
            string savePath = Application.persistentDataPath + "/savegame.json";

            if (System.IO.File.Exists(savePath))
            {
                string json = System.IO.File.ReadAllText(savePath);
                GameSaveData saveData = JsonUtility.FromJson<GameSaveData>(json);

                gameTime = saveData.gameTime;

                OnGameLoaded?.Invoke();

                if (debugMode)
                    Debug.Log($"Game loaded from: {savePath}");
            }
            else
            {
                Debug.LogWarning("No save file found");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load game: {e.Message}");
        }
    }

    private IEnumerator AutoSaveRoutine()
    {
        while (true)
        {
            yield return new WaitForSeconds(autoSaveInterval);

            if (IsInGame)
            {
                SaveGame();
                if (debugMode)
                    Debug.Log("Auto-save completed");
            }
        }
    }

    // Utility Methods
    public float GetGameTime() => gameTime;

    public void ResetGameTime() => gameTime = 0f;

    private void OnDestroy()
    {
        if (autoSaveCoroutine != null)
            StopCoroutine(autoSaveCoroutine);
    }

    private void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus && IsInGame)
            SaveGame();
    }

    private void OnApplicationFocus(bool hasFocus)
    {
        if (!hasFocus && IsInGame)
            SaveGame();
    }
}

[System.Serializable]
public class GameSaveData
{
    public float gameTime;
    public string currentScene;
    public long saveTimestamp;
}