{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 13644, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 13644, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 13644, "tid": 11, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 13644, "tid": 11, "ts": 1752514709222523, "dur": 1053, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 13644, "tid": 11, "ts": 1752514709228469, "dur": 1014, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 13644, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 13644, "tid": 1, "ts": 1752514708921039, "dur": 12198, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 13644, "tid": 1, "ts": 1752514708933241, "dur": 79649, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 13644, "tid": 1, "ts": 1752514709012903, "dur": 67836, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 13644, "tid": 11, "ts": 1752514709229488, "dur": 1367, "ph": "X", "name": "", "args": {}}, {"pid": 13644, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708918838, "dur": 5500, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708924342, "dur": 284810, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708929300, "dur": 4060, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708933367, "dur": 1606, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708934976, "dur": 353, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935340, "dur": 15, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935357, "dur": 54, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935414, "dur": 2, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935417, "dur": 77, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935496, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935498, "dur": 64, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935564, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935566, "dur": 65, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935635, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935712, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935714, "dur": 79, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935795, "dur": 1, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935797, "dur": 73, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935873, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935874, "dur": 75, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935952, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708935954, "dur": 66, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936024, "dur": 34, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936060, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936061, "dur": 31, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936096, "dur": 30, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936130, "dur": 29, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936162, "dur": 25, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936191, "dur": 31, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936226, "dur": 30, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936258, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936260, "dur": 31, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936294, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936325, "dur": 51, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936380, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936430, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936434, "dur": 52, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936489, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936491, "dur": 46, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936539, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936541, "dur": 39, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936583, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936584, "dur": 30, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936617, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936618, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936652, "dur": 33, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936689, "dur": 27, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936719, "dur": 39, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936761, "dur": 32, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936795, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936797, "dur": 33, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936832, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936834, "dur": 38, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936874, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936876, "dur": 38, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936923, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936925, "dur": 54, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936981, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708936983, "dur": 36, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937022, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937023, "dur": 58, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937084, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937086, "dur": 38, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937126, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937127, "dur": 34, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937164, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937166, "dur": 33, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937203, "dur": 37, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937244, "dur": 56, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937303, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937306, "dur": 71, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937379, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937381, "dur": 69, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937452, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937454, "dur": 74, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937530, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937532, "dur": 82, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937618, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937620, "dur": 37, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937659, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708937661, "dur": 428, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938091, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938093, "dur": 124, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938221, "dur": 5, "ph": "X", "name": "ProcessMessages 5429", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938227, "dur": 50, "ph": "X", "name": "ReadAsync 5429", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938278, "dur": 1, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938281, "dur": 34, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938316, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938319, "dur": 38, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938360, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938362, "dur": 34, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938400, "dur": 76, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938479, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938481, "dur": 42, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938526, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938528, "dur": 77, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938608, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938610, "dur": 41, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938653, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938655, "dur": 33, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938691, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938692, "dur": 38, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938734, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938736, "dur": 37, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938775, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938777, "dur": 30, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938810, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938811, "dur": 78, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938892, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938930, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938931, "dur": 32, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938966, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708938967, "dur": 34, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939004, "dur": 33, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939039, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939042, "dur": 30, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939074, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939076, "dur": 195, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939274, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939317, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939318, "dur": 37, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939358, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939360, "dur": 34, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939398, "dur": 36, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939435, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939437, "dur": 31, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939472, "dur": 31, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939506, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939543, "dur": 37, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939582, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939584, "dur": 31, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939619, "dur": 30, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939652, "dur": 31, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939686, "dur": 28, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939718, "dur": 32, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939753, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939790, "dur": 32, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939824, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939826, "dur": 33, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939860, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939863, "dur": 32, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939899, "dur": 32, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939934, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708939965, "dur": 42, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940012, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940059, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940061, "dur": 41, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940105, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940107, "dur": 40, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940149, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940151, "dur": 35, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940189, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940191, "dur": 35, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940229, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940231, "dur": 39, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940272, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940274, "dur": 39, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940315, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940317, "dur": 35, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940354, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940357, "dur": 32, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940393, "dur": 35, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940431, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940433, "dur": 37, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940481, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940483, "dur": 39, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940525, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940526, "dur": 37, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940566, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940568, "dur": 31, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940601, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940604, "dur": 35, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940641, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940642, "dur": 34, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940679, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940681, "dur": 37, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940720, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940722, "dur": 32, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940759, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940799, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940801, "dur": 34, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940838, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940839, "dur": 36, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940878, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940880, "dur": 35, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940918, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940919, "dur": 36, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940958, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708940960, "dur": 38, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941000, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941003, "dur": 33, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941038, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941040, "dur": 42, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941084, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941086, "dur": 36, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941125, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941126, "dur": 35, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941164, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941166, "dur": 38, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941206, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941208, "dur": 33, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941243, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941245, "dur": 36, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941283, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941285, "dur": 35, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941323, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941324, "dur": 37, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941364, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941367, "dur": 52, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941422, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941424, "dur": 33, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941460, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941461, "dur": 36, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941501, "dur": 33, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941536, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941538, "dur": 47, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941588, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941591, "dur": 50, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941645, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941648, "dur": 38, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941689, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941691, "dur": 40, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941734, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941737, "dur": 40, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941779, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941781, "dur": 36, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941819, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941821, "dur": 35, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941858, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941859, "dur": 30, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941892, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941893, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941930, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941932, "dur": 33, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941967, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708941969, "dur": 36, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942009, "dur": 33, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942045, "dur": 32, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942079, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942081, "dur": 34, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942118, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942120, "dur": 29, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942152, "dur": 34, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942189, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942191, "dur": 34, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942227, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942228, "dur": 33, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942264, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942265, "dur": 33, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942301, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942303, "dur": 31, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942337, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942338, "dur": 33, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942375, "dur": 31, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942409, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942410, "dur": 33, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942448, "dur": 53, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942504, "dur": 34, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942540, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942542, "dur": 31, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942575, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942576, "dur": 54, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942635, "dur": 33, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942671, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942672, "dur": 37, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942712, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942713, "dur": 34, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942749, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942751, "dur": 33, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942787, "dur": 32, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942822, "dur": 2, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942824, "dur": 33, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942861, "dur": 32, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942896, "dur": 1, "ph": "X", "name": "ProcessMessages 226", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942897, "dur": 36, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942936, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942937, "dur": 33, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942972, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708942974, "dur": 35, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943011, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943012, "dur": 31, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943046, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943047, "dur": 31, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943082, "dur": 35, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943119, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943121, "dur": 33, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943158, "dur": 33, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943194, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943195, "dur": 33, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943230, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943232, "dur": 37, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943272, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943274, "dur": 45, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943322, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943324, "dur": 36, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943363, "dur": 33, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943400, "dur": 32, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943434, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943436, "dur": 32, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943470, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943472, "dur": 28, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943503, "dur": 29, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943536, "dur": 34, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943572, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943573, "dur": 32, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943609, "dur": 31, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943643, "dur": 33, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943679, "dur": 32, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943713, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943714, "dur": 26, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943743, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943795, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943831, "dur": 50, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943883, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943885, "dur": 32, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943921, "dur": 31, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943955, "dur": 35, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943993, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708943995, "dur": 31, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944029, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944031, "dur": 39, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944073, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944074, "dur": 37, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944114, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944116, "dur": 39, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944157, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944159, "dur": 42, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944203, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944205, "dur": 35, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944243, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944245, "dur": 39, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944286, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944288, "dur": 34, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944324, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944326, "dur": 38, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944367, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944369, "dur": 37, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944409, "dur": 39, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944451, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944453, "dur": 37, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944492, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944494, "dur": 36, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944533, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944535, "dur": 37, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944574, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944576, "dur": 31, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944609, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944610, "dur": 38, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944653, "dur": 36, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944691, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944693, "dur": 36, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944732, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944733, "dur": 33, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944769, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944771, "dur": 39, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944812, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944814, "dur": 38, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944854, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944855, "dur": 33, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944891, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944893, "dur": 37, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944933, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944934, "dur": 36, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944973, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708944975, "dur": 37, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945014, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945016, "dur": 36, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945055, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945057, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945091, "dur": 34, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945128, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945130, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945190, "dur": 37, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945229, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945231, "dur": 51, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945285, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945324, "dur": 36, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945363, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945364, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945404, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945406, "dur": 30, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945438, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945440, "dur": 101, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945545, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945587, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945589, "dur": 36, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945628, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945630, "dur": 33, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945665, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945667, "dur": 94, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945764, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945806, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945808, "dur": 36, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945846, "dur": 8, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945856, "dur": 33, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945891, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945892, "dur": 77, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708945973, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946013, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946015, "dur": 38, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946056, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946057, "dur": 29, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946090, "dur": 79, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946173, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946213, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946215, "dur": 37, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946256, "dur": 33, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946291, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946293, "dur": 90, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946387, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946427, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946429, "dur": 36, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946467, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946469, "dur": 36, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946507, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946509, "dur": 98, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946610, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946651, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946652, "dur": 40, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946695, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946696, "dur": 30, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946730, "dur": 86, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946819, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946863, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946865, "dur": 42, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946910, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946912, "dur": 32, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946946, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708946948, "dur": 157, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947109, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947151, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947153, "dur": 38, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947193, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947194, "dur": 31, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947228, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947230, "dur": 82, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947316, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947356, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947358, "dur": 39, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947400, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947401, "dur": 37, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947441, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947443, "dur": 37, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947482, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947484, "dur": 39, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947526, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947527, "dur": 31, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947561, "dur": 8, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947570, "dur": 41, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947614, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947616, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947686, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947726, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947728, "dur": 44, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947775, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947817, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947819, "dur": 38, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947859, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947861, "dur": 36, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947899, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947901, "dur": 37, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947940, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947942, "dur": 35, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947979, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708947981, "dur": 35, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948020, "dur": 104, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948128, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948168, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948170, "dur": 39, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948212, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948213, "dur": 35, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948251, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948253, "dur": 31, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948288, "dur": 38, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948328, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948330, "dur": 38, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948371, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948372, "dur": 30, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948405, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948407, "dur": 32, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948443, "dur": 85, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948532, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948571, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948573, "dur": 35, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948611, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948613, "dur": 38, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948654, "dur": 56, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948713, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948715, "dur": 34, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948751, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948752, "dur": 31, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948786, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948787, "dur": 35, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948825, "dur": 31, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948860, "dur": 34, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948896, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708948898, "dur": 100, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949002, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949047, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949048, "dur": 39, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949090, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949091, "dur": 32, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949126, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949127, "dur": 81, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949211, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949262, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949264, "dur": 42, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949309, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949312, "dur": 41, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949355, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949357, "dur": 75, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949436, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949480, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949482, "dur": 38, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949523, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949525, "dur": 44, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949571, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949573, "dur": 82, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949659, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949708, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949710, "dur": 33, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949746, "dur": 38, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949787, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949788, "dur": 34, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949825, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949826, "dur": 88, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949918, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949961, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708949963, "dur": 40, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950005, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950007, "dur": 33, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950043, "dur": 91, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950138, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950181, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950182, "dur": 39, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950224, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950225, "dur": 37, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950265, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950267, "dur": 36, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950306, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950307, "dur": 85, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950395, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950438, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950440, "dur": 38, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950480, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950482, "dur": 39, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950523, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950525, "dur": 31, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950560, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950601, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950602, "dur": 39, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950644, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950646, "dur": 36, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950684, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950685, "dur": 35, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950723, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950725, "dur": 92, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950820, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950862, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950863, "dur": 40, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950906, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950908, "dur": 32, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950942, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708950944, "dur": 90, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951038, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951080, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951082, "dur": 40, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951124, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951126, "dur": 38, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951167, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951168, "dur": 34, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951205, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951207, "dur": 38, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951248, "dur": 39, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951290, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951292, "dur": 34, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951329, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951330, "dur": 89, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951423, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951464, "dur": 38, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951504, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951506, "dur": 38, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951546, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951548, "dur": 37, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951588, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951589, "dur": 45, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951638, "dur": 41, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951685, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951687, "dur": 33, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951724, "dur": 34, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951761, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951762, "dur": 115, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951881, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951922, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951924, "dur": 40, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708951968, "dur": 38, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952009, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952011, "dur": 38, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952052, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952053, "dur": 39, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952095, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952097, "dur": 34, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952135, "dur": 35, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952172, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952174, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952259, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952305, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952306, "dur": 44, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952353, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952355, "dur": 31, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952390, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952496, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952543, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952545, "dur": 41, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952588, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952590, "dur": 42, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952637, "dur": 37, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952676, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952678, "dur": 36, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952717, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952719, "dur": 34, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952755, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952760, "dur": 34, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952797, "dur": 36, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952837, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952924, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952964, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708952965, "dur": 33, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953000, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953002, "dur": 37, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953042, "dur": 29, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953075, "dur": 34, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953111, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953112, "dur": 32, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953148, "dur": 29, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953180, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953214, "dur": 30, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953248, "dur": 88, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953340, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953377, "dur": 31, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953411, "dur": 35, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953449, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953451, "dur": 36, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953489, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953491, "dur": 31, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953526, "dur": 33, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953561, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953563, "dur": 34, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953600, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953601, "dur": 26, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953631, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953670, "dur": 36, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953708, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953710, "dur": 76, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953789, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953830, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953832, "dur": 36, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953870, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953872, "dur": 34, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708953910, "dur": 88, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954002, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954043, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954045, "dur": 35, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954083, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954085, "dur": 36, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954123, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954125, "dur": 39, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954166, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954167, "dur": 36, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954207, "dur": 33, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954243, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954245, "dur": 31, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954279, "dur": 114, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954397, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954435, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954436, "dur": 47, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954485, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954487, "dur": 34, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954524, "dur": 35, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954563, "dur": 34, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954598, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954600, "dur": 35, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954638, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954639, "dur": 35, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954677, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954678, "dur": 33, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954713, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954715, "dur": 34, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954752, "dur": 28, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954783, "dur": 84, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954873, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954920, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954922, "dur": 36, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954962, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708954964, "dur": 33, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708955005, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708955102, "dur": 1730, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708956836, "dur": 188, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957026, "dur": 10, "ph": "X", "name": "ProcessMessages 13136", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957037, "dur": 35, "ph": "X", "name": "ReadAsync 13136", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957076, "dur": 31, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957111, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957112, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957147, "dur": 85, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957236, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957275, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957277, "dur": 39, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957318, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957320, "dur": 29, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957353, "dur": 88, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957445, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957493, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957496, "dur": 38, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957536, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957538, "dur": 93, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957635, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957675, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957677, "dur": 37, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957717, "dur": 5, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957723, "dur": 37, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957764, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957766, "dur": 33, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957801, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957804, "dur": 38, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957844, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957847, "dur": 33, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957885, "dur": 33, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708957922, "dur": 92, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708958020, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708958057, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708958063, "dur": 42, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708958109, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708958111, "dur": 38, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708958152, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708958154, "dur": 3067, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708961229, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708961233, "dur": 262, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708961497, "dur": 388, "ph": "X", "name": "ProcessMessages 12028", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708961889, "dur": 71, "ph": "X", "name": "ReadAsync 12028", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708961963, "dur": 5, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708961970, "dur": 44, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962019, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962022, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962066, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962071, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962113, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962116, "dur": 38, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962157, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962160, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962200, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962203, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962245, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962248, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962288, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962290, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962338, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962341, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962381, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962383, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962429, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962432, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962477, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962480, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962532, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962535, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962575, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962578, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962618, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962621, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962661, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962663, "dur": 46, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962713, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962716, "dur": 40, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962760, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708962763, "dur": 1429, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964197, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964200, "dur": 132, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964336, "dur": 21, "ph": "X", "name": "ProcessMessages 2736", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964359, "dur": 43, "ph": "X", "name": "ReadAsync 2736", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964406, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964409, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964465, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964468, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964512, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964515, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964555, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964557, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964602, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964605, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964656, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964658, "dur": 40, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964702, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964705, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964746, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964749, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964792, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964794, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964833, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964835, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964875, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964877, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964917, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964920, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964956, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708964959, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965003, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965006, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965044, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965047, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965085, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965088, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965198, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965246, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708965248, "dur": 18949, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708984205, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708984209, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708984267, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708984271, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708984316, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708984319, "dur": 5154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989480, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989484, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989547, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989550, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989593, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989595, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989637, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989640, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989862, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989864, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989911, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514708989914, "dur": 20007, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709009928, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709009932, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709010020, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709010023, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709010066, "dur": 910, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709010982, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011023, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011112, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011114, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011160, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011163, "dur": 281, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011450, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011509, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011512, "dur": 56, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011571, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011574, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011609, "dur": 249, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011861, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011863, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011904, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709011906, "dur": 416, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012328, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012364, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012366, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012408, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012459, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012461, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012503, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012505, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012544, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012546, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012580, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012582, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012641, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012678, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012680, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012729, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012731, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012798, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012842, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012844, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012883, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012885, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012921, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012924, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012955, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012957, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012997, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709012999, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013054, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013096, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013098, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013136, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013138, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013173, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013175, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013327, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013369, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013371, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013408, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013410, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013573, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013617, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013619, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013666, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013702, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013704, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013742, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013745, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013823, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013863, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013866, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013917, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013919, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013960, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709013963, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014003, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014006, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014130, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014168, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014170, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014209, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014211, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014257, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014260, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014318, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014320, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014361, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014363, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014403, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014405, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014444, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014446, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014477, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014479, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014518, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014521, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014577, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014579, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014622, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014624, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014668, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014670, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014721, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014724, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014772, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014774, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014803, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014805, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014869, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014872, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014919, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709014922, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015114, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015116, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015161, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015163, "dur": 261, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015429, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015431, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015512, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015514, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015565, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015567, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015609, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015611, "dur": 43, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015657, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015659, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015714, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015717, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015767, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015770, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015909, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015958, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709015960, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016006, "dur": 419, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016430, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016477, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016479, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016534, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016537, "dur": 46, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016586, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016588, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016657, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016709, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016712, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016765, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016767, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016816, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016818, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016911, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016914, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016970, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709016972, "dur": 222, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017199, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017286, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017289, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017342, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017345, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017556, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017558, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017610, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017612, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017661, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709017663, "dur": 487, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018156, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018197, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018242, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018279, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018283, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018398, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018401, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018450, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018453, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018505, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018553, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018555, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018593, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018595, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018636, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018638, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018674, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018676, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709018712, "dur": 719, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019435, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019499, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019502, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019552, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019554, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019601, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019603, "dur": 365, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019973, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709019976, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020030, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020033, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020083, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020085, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020137, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020139, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020187, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020198, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020280, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020327, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020329, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020373, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020380, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020426, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020428, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020481, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709020484, "dur": 554, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021045, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021098, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021101, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021149, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021150, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021198, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021200, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021299, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021341, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021402, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021404, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021453, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021454, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021503, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021505, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021673, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021675, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021720, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709021722, "dur": 454, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709022180, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709022260, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709022695, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709022773, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709022775, "dur": 251, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709023031, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709023079, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709023082, "dur": 740, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709023827, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709023871, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709024055, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709024099, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709024102, "dur": 413, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709024520, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709024562, "dur": 135663, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709160233, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709160237, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709160292, "dur": 1608, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709161903, "dur": 16217, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178130, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178134, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178187, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178190, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178229, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178231, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178281, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178283, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178361, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178373, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178411, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178414, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178535, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178572, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178574, "dur": 193, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178772, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178808, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709178811, "dur": 263, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709179079, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709179112, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709179114, "dur": 775, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709179894, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709179929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709179931, "dur": 1415, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181352, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181401, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181404, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181647, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181686, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181688, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181733, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181765, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181767, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181849, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181884, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181886, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181931, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181933, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181967, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709181970, "dur": 297, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182272, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182307, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182309, "dur": 180, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182494, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182539, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182541, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182613, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182615, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182825, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182827, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182861, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709182863, "dur": 552, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709183419, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709183421, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709183454, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709183456, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709183491, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709183493, "dur": 1218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709184717, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709184750, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709184752, "dur": 460, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185218, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185259, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185261, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185302, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185512, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185554, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185557, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185602, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185652, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185654, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185692, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185694, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185740, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185743, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185786, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185788, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185834, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185836, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185873, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185876, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185921, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185959, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709185961, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186014, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186052, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186055, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186093, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186095, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186133, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186134, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186184, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186221, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186223, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186264, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186266, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186288, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186332, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186334, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186367, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186369, "dur": 39, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186411, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186413, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186455, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186458, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186509, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186549, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186551, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186588, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186590, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186631, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186666, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186669, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186712, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186714, "dur": 41, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186758, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186760, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186798, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186800, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186841, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186843, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186892, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186894, "dur": 33, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186932, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709186964, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187005, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187007, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187040, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187042, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187076, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187110, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187113, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187146, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187149, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187184, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187186, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187221, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187223, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187255, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187288, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187290, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187345, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187349, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187397, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187399, "dur": 593, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709187999, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188002, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188043, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188045, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188095, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188097, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188146, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188192, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188194, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188241, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188288, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188290, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188333, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188334, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188379, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188383, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188430, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188432, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188514, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188516, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188563, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188565, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188608, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188660, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188661, "dur": 144, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188811, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188856, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188859, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188905, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709188907, "dur": 733, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709189646, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709189755, "dur": 394, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 13644, "tid": 12884901888, "ts": 1752514709190153, "dur": 18142, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 13644, "tid": 11, "ts": 1752514709230858, "dur": 2183, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 13644, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 13644, "tid": 8589934592, "ts": 1752514708915738, "dur": 165036, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 13644, "tid": 8589934592, "ts": 1752514709080778, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 13644, "tid": 8589934592, "ts": 1752514709080786, "dur": 1931, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 13644, "tid": 11, "ts": 1752514709233043, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 13644, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 13644, "tid": 4294967296, "ts": 1752514708879937, "dur": 330353, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 13644, "tid": 4294967296, "ts": 1752514708887286, "dur": 20435, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 13644, "tid": 4294967296, "ts": 1752514709210508, "dur": 8731, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 13644, "tid": 4294967296, "ts": 1752514709213980, "dur": 3779, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 13644, "tid": 4294967296, "ts": 1752514709219321, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 13644, "tid": 11, "ts": 1752514709233050, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752514708920659, "dur": 52, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514708920744, "dur": 3837, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514708924600, "dur": 1323, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514708926121, "dur": 103, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752514708926225, "dur": 833, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514708928353, "dur": 3243, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_899A378C13AF1A22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752514708932839, "dur": 2633, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752514708936036, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752514708936427, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752514708938254, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_7989A1C8D57EF9BF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752514708944498, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752514708953617, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752514708957003, "dur": 215, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752514708927090, "dur": 32628, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514708959734, "dur": 229306, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514709189042, "dur": 290, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514709189347, "dur": 120, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514709189615, "dur": 95, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514709189743, "dur": 4100, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752514708926872, "dur": 32872, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708959775, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708959923, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7352044D91930000.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708960503, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708960601, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_64594791FA428B38.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708960670, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708960750, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_E840454B465E9DA1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708960823, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708961200, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_96FD8E6A58B6181D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708961260, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708961352, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_BE65CD6E35D7EF6C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708961417, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708961538, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_BE65CD6E35D7EF6C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708961813, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_A4DFE5E4F726E82A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708961887, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708962154, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708962285, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708962476, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708962603, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514708962788, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708962870, "dur": 21175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752514708984046, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708984330, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708984450, "dur": 2045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708986496, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708989083, "dur": 2612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708991695, "dur": 2914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708994609, "dur": 3160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708997770, "dur": 2119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514708999890, "dur": 2362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709002252, "dur": 2470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709004723, "dur": 2071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709006795, "dur": 829, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@7056e7f856e9\\UnityEditor.TestRunner\\TestLaunchers\\Helpers\\PlayerLauncherBuildOptions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752514709006795, "dur": 2209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709009005, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709009780, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709011284, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514709011601, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709011712, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752514709014142, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709014380, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752514709014591, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709014708, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752514709015328, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709015573, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709015700, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709015793, "dur": 906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709016703, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709016786, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709017439, "dur": 71502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709088941, "dur": 86192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709175135, "dur": 3590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752514709178726, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709178914, "dur": 3222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752514709182137, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709182437, "dur": 3142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752514709185580, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709185808, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186092, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186169, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186251, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186325, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186396, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186491, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186578, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186670, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186815, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709186917, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709187054, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709187330, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709187402, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709187462, "dur": 1245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709188711, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752514709188772, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708926922, "dur": 32854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708959783, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1803B24A9A5F24CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708960353, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708960433, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_90A3A1756AFEB403.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708960510, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708960620, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_46336BF2CD3CD8B6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708960694, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708960780, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_36816116B504448E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708960848, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708960927, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E54A1EA9E3C11DA9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708960994, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708961075, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BACA9FCF1825810F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708961146, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708961224, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_BBB37354D5B752B0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708961408, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708961489, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_BBB37354D5B752B0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708961722, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_2C39E7608D29F332.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708961794, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708961888, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_CDD9368B964154AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708961967, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708962073, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_6D2A4087270D7EA3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708962148, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708962256, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708962359, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708962543, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708962620, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_77BE30E9EA33DCEC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514708962719, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708962808, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708962909, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963017, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963101, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752514708963165, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963251, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963364, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963464, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963541, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963624, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963746, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963821, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708963908, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708964014, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708964133, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708964292, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752514708964360, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708964449, "dur": 356, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752514708964834, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708964944, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708965047, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708965116, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708967224, "dur": 2435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708970549, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.11f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\clrjit.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752514708969659, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708971720, "dur": 2178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708973898, "dur": 2617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708976516, "dur": 2309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708978826, "dur": 2304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708981131, "dur": 2171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708983302, "dur": 2613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708985916, "dur": 2675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708988592, "dur": 2255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708990847, "dur": 2013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708992861, "dur": 2206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708995067, "dur": 2181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708997249, "dur": 2438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514708999688, "dur": 2466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709002155, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709004300, "dur": 2444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709006745, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709008856, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709009809, "dur": 1498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709011308, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514709011508, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709011615, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752514709012348, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709012786, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709013024, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514709013364, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709013463, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752514709014249, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709014509, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709014584, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709014646, "dur": 1127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709015784, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514709015985, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709016063, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752514709016716, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709016929, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709017002, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709017431, "dur": 1336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709018768, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514709018930, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709019040, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752514709019915, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709020120, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709020184, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752514709020325, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709020410, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752514709020951, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709021171, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709021277, "dur": 153883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709175161, "dur": 3340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752514709178506, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709178677, "dur": 3576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752514709182254, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709182637, "dur": 3391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752514709186029, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709186195, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709186329, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709186454, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709186588, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709186660, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709186765, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709186841, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709186927, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709187033, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709187104, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709187172, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709187271, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709187371, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709187443, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709188437, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752514709188509, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708927227, "dur": 32664, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708959901, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FD0B7C7134C4E74F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708960459, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708960554, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4E7E52EF093EFD84.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708960623, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708960702, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_1353754487495D10.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708960768, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708960851, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_5405AA24FDD954DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708960916, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708961021, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_A6CCBCBB862E82EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708961092, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708961258, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A62FB27282B4CD9F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708961368, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708961479, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A62FB27282B4CD9F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708961676, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_CD5E8DB87CFE5A1E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708961747, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708961839, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_0F8D4C46C56ED03D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708961937, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708962061, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708962160, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708962254, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708962426, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708962507, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708962583, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_B4C05A6AB20AC9DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514708962652, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708962737, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708962822, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708962918, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963034, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963119, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963192, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752514708963248, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963328, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963437, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963666, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963750, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752514708963807, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963896, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708963971, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708964051, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708964223, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708964377, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752514708964517, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708964647, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708964759, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708964874, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708965000, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708965077, "dur": 2762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708967840, "dur": 2024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708969865, "dur": 2733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708972599, "dur": 2103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708974703, "dur": 2274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708976978, "dur": 2280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708979258, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708981404, "dur": 1824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708983497, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Framework\\Control\\ForAnalyser.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752514708983228, "dur": 2898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708986127, "dur": 2219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708988347, "dur": 2416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708990764, "dur": 2373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708993138, "dur": 2026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708995165, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708997222, "dur": 2128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514708999350, "dur": 2253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709001604, "dur": 2069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709003674, "dur": 2090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709005805, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709006138, "dur": 2288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709008427, "dur": 1387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709009814, "dur": 1460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709011276, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514709011524, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709011656, "dur": 358, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514709012016, "dur": 1425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752514709013442, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709013730, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709013807, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709014652, "dur": 1123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709015777, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752514709015960, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709016037, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752514709016431, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709016640, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709016721, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709017446, "dur": 158525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709175974, "dur": 3524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752514709179504, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709180035, "dur": 3430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752514709183466, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709183624, "dur": 3308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752514709186933, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709187162, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709187229, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709187456, "dur": 1225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709188685, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752514709188751, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708926965, "dur": 32823, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708959798, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_204DE5D6C842BB15.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708960336, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708960419, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B17360C5D39B0E61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708960472, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708960537, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_03EA69CA42D5B8CA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708960641, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708960724, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_39BF2EB6281B0BED.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708960793, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708960883, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_F30211A6159635E4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708960951, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708961035, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2F151598140774A9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708961107, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708961408, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B7B05FD5C3B5AAE5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708961524, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708961591, "dur": 388, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B7B05FD5C3B5AAE5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708961987, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_CC0F443DDAA431B4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708962055, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708962331, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708962473, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708962550, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708962699, "dur": 26549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514708989250, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708989618, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708989715, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514708989896, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514708989998, "dur": 19468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709009468, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709009797, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709009893, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514709010072, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709010158, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709010848, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709011115, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709011285, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514709011482, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709011572, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514709011669, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709012462, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709012699, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709012788, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514709013160, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709013236, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709014300, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709014538, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514709014743, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709014814, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709015569, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709015869, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709016701, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709017432, "dur": 2781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709020215, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514709020410, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709020505, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709022546, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709022769, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709022842, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514709023018, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709023089, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709023728, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709023928, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709023996, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752514709024095, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709024157, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709024486, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709024649, "dur": 150506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709175156, "dur": 2764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709177922, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709178241, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709178344, "dur": 3331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709181676, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709181873, "dur": 3625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709185499, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709185673, "dur": 2895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752514709188569, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709188702, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752514709188775, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708927010, "dur": 32791, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708959811, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_ECFDFE822925AC1A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514708960412, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708960490, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_34A92FB9474D54E9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514708960596, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708960672, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_EC4523A63DC8AC69.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514708960747, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708960842, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_A431789CF4E2CEDF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514708960911, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708961195, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3198239738CF1C15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514708961385, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708961560, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3198239738CF1C15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514708961877, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_468062CF62E83C20.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514708961943, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708962190, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708962295, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752514708962362, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708962494, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708962585, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708962659, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752514708962888, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708962965, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752514708963020, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708963386, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708963495, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708963577, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708963677, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708963765, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708963836, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708963923, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708964041, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708964140, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708964245, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752514708964353, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708964431, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752514708964657, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708964771, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708964873, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708964940, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752514708965206, "dur": 2032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708967239, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708968923, "dur": 1995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708970918, "dur": 2544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708973462, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708975367, "dur": 2030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708977397, "dur": 2287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708979685, "dur": 2120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708981806, "dur": 2421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708984228, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708985938, "dur": 2363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708988302, "dur": 2125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708990427, "dur": 2255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708992683, "dur": 2692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708995376, "dur": 2329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514708997706, "dur": 2304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709000011, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709001655, "dur": 2532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709004187, "dur": 2969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709007157, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709008313, "dur": 1473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709009786, "dur": 1530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709011318, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514709011518, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709011680, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709012410, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709012663, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514709012831, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709012938, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709013035, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514709013368, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709013506, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709015002, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709015258, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709015772, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514709015972, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709016067, "dur": 1058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709017125, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709017331, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709017424, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514709017619, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709017712, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709018410, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709018669, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709018762, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514709018922, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709019013, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709019845, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709020110, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709020204, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752514709020383, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709020509, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709020977, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709021191, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709021262, "dur": 153900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709175165, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709177922, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709178261, "dur": 2965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709181227, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709181783, "dur": 3416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709185200, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709185343, "dur": 2806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752514709188153, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709188280, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752514709188345, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708927055, "dur": 32762, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708959827, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1D42C13E66B93AAE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708960366, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708960436, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A2D423709B0EF184.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708960495, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708960608, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_D8D0394314A4A2D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708960686, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708960822, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1E9646670035E0A5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708960891, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708960969, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_131D4066C656F2E9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708961036, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708961114, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_B822F520279A6525.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708961177, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708961270, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_3C135DD4F3589C91.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708961420, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708961503, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_3C135DD4F3589C91.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708961749, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_E8EEB29348A2B6EC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708961824, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708961914, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D253BE63DEE6818A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708961990, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708962081, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_6C99DF3255754733.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514708962194, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708962379, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708962469, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752514708962588, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708962721, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708962825, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708963043, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708963152, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708963279, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708963433, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708963525, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708963601, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708963893, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708963977, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708964058, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708964136, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708964281, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708964384, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752514708964560, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708964666, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708964817, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708964932, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708965036, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708965108, "dur": 2273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708967382, "dur": 1972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708969355, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708971486, "dur": 2114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708973600, "dur": 2005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708977873, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Math\\Trigonometry\\Arctangent2Node.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752514708975606, "dur": 2823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708978429, "dur": 2542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708980971, "dur": 1616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708982587, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708985103, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Ports\\IUnitOutputPortDefinition.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752514708984869, "dur": 3002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708987871, "dur": 2408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708990279, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708992209, "dur": 2457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708994666, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708996407, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514708998671, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709001259, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709003075, "dur": 2204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709005280, "dur": 2222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709007870, "dur": 1907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709009778, "dur": 1489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709011286, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514709011487, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709011657, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514709011999, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752514709012734, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709012983, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709013065, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752514709014243, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709014720, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709014797, "dur": 995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709015793, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709016701, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709016801, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709017435, "dur": 2869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709020305, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752514709020490, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709020579, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752514709021201, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709021432, "dur": 153725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709175158, "dur": 3132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752514709178296, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709178505, "dur": 3213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752514709181719, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709182100, "dur": 4257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752514709186357, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709186515, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709186579, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709186650, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709186722, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709186807, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709186900, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709187035, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709187110, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709187179, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709187307, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752514709187474, "dur": 1569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708927107, "dur": 32725, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708959842, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_EDE74100883972FC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708960399, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708960487, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_83FF70501C8E89D0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708960592, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708960675, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_D4315AC9A8E2EBC1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708960752, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708960863, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_E2480D2CFF51CBAB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708960932, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708961027, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B017602BDE927141.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708961135, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708961254, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1895A18B5AB925EF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708961380, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708961484, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1895A18B5AB925EF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708961706, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_07708FBFB92278A4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708961804, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708961908, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_A88FBEF1A1D34394.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708961988, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708962117, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AC1B969D298A1FF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708962208, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708962300, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_3380033AF840AC33.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514708962488, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708962579, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752514708962636, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708962719, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752514708962777, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708962863, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708962964, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708963073, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708963212, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708963308, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708963401, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708963531, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708963653, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708963772, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708963865, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708964022, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708964091, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752514708964190, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708964270, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752514708964322, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708964417, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752514708964624, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708964741, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708964988, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752514708965076, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708965330, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708967306, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708968680, "dur": 2050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708970730, "dur": 1928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708972659, "dur": 2112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708974771, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708976479, "dur": 2818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708979298, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708981172, "dur": 1827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708983000, "dur": 2473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708985473, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708987358, "dur": 2719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708990078, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708992428, "dur": 2630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708995059, "dur": 2052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514708997111, "dur": 2319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709000121, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\InputSystemObject.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752514708999431, "dur": 2984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709002416, "dur": 2813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709005230, "dur": 2659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709007890, "dur": 1901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709009791, "dur": 1751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709011544, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752514709011851, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709012014, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752514709012757, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709013297, "dur": 1211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709014508, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709014590, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709014771, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709014855, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709015787, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709016714, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709017437, "dur": 65469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709087140, "dur": 298, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1752514709087439, "dur": 1361, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1752514709088800, "dur": 127, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1752514709082908, "dur": 6026, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709088935, "dur": 86214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709175151, "dur": 3141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752514709178297, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709178517, "dur": 4275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752514709182793, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709182964, "dur": 3420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752514709186385, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709186566, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709186646, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709186715, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709186786, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709186900, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709187029, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709187244, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709187331, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709187407, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709188138, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752514709188213, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708927144, "dur": 32706, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708959862, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_19F2ED932A7B056A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514708960436, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708960542, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_AD0C8612842407A2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514708960639, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708960741, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A7AB5C884021046E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514708960808, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708960886, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_542B156078FA0B08.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514708960953, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708961040, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0F9F19BDE3C2DB81.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514708961258, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708961407, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8F2C9ED41342D533.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514708961498, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708961576, "dur": 307, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8F2C9ED41342D533.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514708961894, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_60A78880124F8A76.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514708961962, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708962109, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708962190, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708962328, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752514708962472, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708962549, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752514708962648, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708962791, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708962903, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708963068, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708963186, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708963338, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708963460, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752514708963516, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708963605, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708963694, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708963789, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708963869, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708964036, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708964143, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708964312, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708964382, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752514708964542, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708964663, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708964777, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752514708964904, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708965020, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708965100, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708966990, "dur": 2698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708969689, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708971512, "dur": 2431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708973943, "dur": 1965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708975909, "dur": 1966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708977949, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Graphs\\GradientMaterialSlot.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752514708977876, "dur": 2814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708980691, "dur": 2262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708982954, "dur": 1986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708984941, "dur": 2229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708987171, "dur": 2123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708989294, "dur": 2202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708991497, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708993256, "dur": 1937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708996472, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Input\\MouseButton.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752514708995194, "dur": 2719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514708997913, "dur": 2051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709000735, "dur": 615, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\InputDiagnostics.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752514708999965, "dur": 2679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709002645, "dur": 1928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709004574, "dur": 1949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709006524, "dur": 2156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709008680, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709009789, "dur": 1474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709011264, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514709011546, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709011679, "dur": 1873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752514709013553, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709014135, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709014371, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514709014548, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709014625, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752514709015377, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709015640, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709015775, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752514709015988, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709016071, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752514709016466, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709016701, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1752514709017603, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709017666, "dur": 226, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709018948, "dur": 141417, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1752514709175150, "dur": 3161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752514709178313, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709178918, "dur": 3049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752514709181968, "dur": 1580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709183561, "dur": 3088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752514709186650, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709186796, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709186883, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709187054, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709187132, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709187204, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709187311, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709187383, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709187469, "dur": 1455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752514709188928, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708927179, "dur": 32690, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708959880, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_8DCCB8CD9AF0526B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708960482, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708960595, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_CA787448257E2ED2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708960666, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708960775, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5587F5557E552B2B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708960906, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708960984, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_5E3037A0D07FB014.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708961051, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708961131, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_AF7FB2451DB90CC9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708961196, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708961275, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C01D1310FB1EC2FC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708961434, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708961509, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C01D1310FB1EC2FC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708961761, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_5ADCA3F0344F316D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708961838, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708961934, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_E57CCA507AA51D29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708962011, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708962094, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_E57CCA507AA51D29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708962322, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708962398, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_CEF96CF5640B5F47.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514708962481, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708962569, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708962637, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752514708962740, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708962850, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963040, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752514708963118, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963280, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963446, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963529, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963613, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963730, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963825, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963915, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708963993, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708964077, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708964184, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708964319, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708964400, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752514708964580, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708964690, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708964805, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708964925, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708965031, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708965103, "dur": 2189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708967292, "dur": 2112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708969405, "dur": 2490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708972834, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@3f70d3ddca99\\Editor\\2D\\ShapeEditor\\EditorTool\\ScriptablePathInspector.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752514708971896, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708974042, "dur": 2011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708976054, "dur": 2581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708978635, "dur": 2067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708980702, "dur": 2403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708983106, "dur": 2796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708985903, "dur": 2745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708988649, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708990629, "dur": 1950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708992580, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708994464, "dur": 2681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708997146, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514708999090, "dur": 2602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709001692, "dur": 2868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709004561, "dur": 2511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709007073, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709008721, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709009780, "dur": 1492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709011274, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514709011578, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709011659, "dur": 884, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514709012545, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752514709013526, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709013749, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709013831, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514709014034, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709014117, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752514709014682, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709014920, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709014997, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514709015158, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709015277, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752514709015846, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709016102, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709016704, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709017427, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752514709017620, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709017785, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752514709018412, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709018658, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709018746, "dur": 156401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709175149, "dur": 3320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752514709178470, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709178687, "dur": 3123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752514709181811, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709181984, "dur": 3597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752514709185582, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709185747, "dur": 792, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709186550, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709186775, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709186907, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709187007, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752514709187206, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709187265, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709187352, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709187444, "dur": 1082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709188529, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752514709188585, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708926903, "dur": 32859, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708959781, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708959924, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A95B1D16B2F8EBB0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708960697, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708960914, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_75AFA7592224BA0C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708960986, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708961079, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C911DEC140E03A4A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708961150, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708961249, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_AB918FC585744211.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708961381, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708961482, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_AB918FC585744211.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708961691, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_850F2892AE5A1CB7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708961748, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708961833, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_4C0AA168B300365B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708961902, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708961980, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_0B12B7EB4009E12D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708962045, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708962130, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_0B12B7EB4009E12D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708962287, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9E4483DA2AD9F364.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514708962375, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708962524, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708962674, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752514708962787, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708962889, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963016, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963106, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963193, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963323, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963407, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963499, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963598, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963686, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963767, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963843, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708963930, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708964089, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708964168, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708964294, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708964403, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708964505, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708964610, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708964711, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708964780, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752514708964940, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708965070, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708965166, "dur": 2386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708967553, "dur": 1908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708969462, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708971210, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708973255, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708975095, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708977030, "dur": 2365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708979396, "dur": 2176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708983197, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@c8192b95703a\\Editor\\Debugging\\DebugWindow.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752514708984215, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@c8192b95703a\\Editor\\Debugging\\DebugUIDrawer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752514708981573, "dur": 3191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708984764, "dur": 2455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708987220, "dur": 3104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708990325, "dur": 2182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708992508, "dur": 2262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708994771, "dur": 2453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708997225, "dur": 2391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514708999617, "dur": 2727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709002345, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709004175, "dur": 2063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709006239, "dur": 2093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709008332, "dur": 1440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709009777, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709009864, "dur": 1406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709011272, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514709011538, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709011648, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752514709012431, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709012972, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709013109, "dur": 1276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709014412, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709014530, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752514709014706, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709014823, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752514709015469, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709015777, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709016720, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709017444, "dur": 157692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709175137, "dur": 3338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752514709178476, "dur": 732, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709179221, "dur": 3322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752514709182544, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709182694, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752514709182748, "dur": 5606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752514709188358, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709188523, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752514709188589, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708927265, "dur": 32634, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708959908, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D1BC1ABB06135D7B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708960597, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708960691, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F574CE38B2B3BAC4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708960760, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708960846, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_9624B07FCD808476.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708960915, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708961045, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_9166293A0F7661BF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708961123, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708961207, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_36E98500AB2FED36.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708961385, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708961462, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_36E98500AB2FED36.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708961661, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_191944D399836D95.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708961730, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708961818, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_B2D255A1965E1827.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708961896, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708962131, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708962220, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708962349, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708962445, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708962605, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_33352EA77CF2B9C9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514708962690, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708962766, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752514708962846, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708962989, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963069, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963169, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963267, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963403, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963532, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963623, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963694, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752514708963764, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963847, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708963938, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708964019, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708964125, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708964249, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708964363, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708964433, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752514708964684, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708964814, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708964991, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708965070, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708966910, "dur": 2031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708968941, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708970358, "dur": 2474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708972832, "dur": 2173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708975005, "dur": 1820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708976825, "dur": 2374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708979200, "dur": 2244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708981445, "dur": 2016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708983462, "dur": 2455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708985918, "dur": 2335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708988253, "dur": 2179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708990433, "dur": 2297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708992730, "dur": 2525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708995255, "dur": 2157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708997413, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514708999190, "dur": 2914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709002105, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\Noise\\classicnoise3D.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752514709002105, "dur": 2506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709004611, "dur": 1921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709006532, "dur": 2177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709008710, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709009811, "dur": 1467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709011279, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514709011500, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709011602, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752514709012366, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709012631, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709012703, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514709012925, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709013047, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752514709014000, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709014405, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709014629, "dur": 1149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709015779, "dur": 936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709016715, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709017439, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752514709017573, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709017650, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752514709018065, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709018289, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709018384, "dur": 156754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709175139, "dur": 3533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752514709178673, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709178909, "dur": 2676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752514709181589, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709182079, "dur": 3435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752514709185515, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709185744, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709185889, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709185976, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186063, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186158, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186245, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186331, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186448, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186528, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186676, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186836, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709186923, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709187046, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709187120, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709187188, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709187255, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709187335, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709187407, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752514709187479, "dur": 1559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708927299, "dur": 32617, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708959925, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5660BA964FD70D04.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708960556, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708960637, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_A29DC2D5B874F8AC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708960705, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708960795, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_2AF506AE32EB7977.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708960867, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708960946, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_40CDD0C2B081259D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708961016, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708961103, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_513A98254D7D8641.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708961175, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708961279, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_899A378C13AF1A22.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708961443, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708961522, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_899A378C13AF1A22.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708961778, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A9144C5927AF0195.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708961852, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708961940, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_320FC6A01F712786.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708962008, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708962075, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_320FC6A01F712786.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708962166, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514708962273, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708962393, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708962466, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752514708962580, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708962725, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708963008, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752514708963102, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708963255, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708963342, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708963454, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708963734, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708963829, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708963928, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964010, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964085, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964164, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964259, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964338, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752514708964411, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964515, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964638, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964750, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964853, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708964989, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708965091, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708965200, "dur": 2136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708967336, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708968952, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708970881, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708972750, "dur": 2511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708975262, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708976798, "dur": 2521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708979319, "dur": 2368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708981688, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708983491, "dur": 2529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708986021, "dur": 2161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708988183, "dur": 2464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708990648, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708992433, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708994275, "dur": 2051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708996327, "dur": 2271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514708998599, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709000881, "dur": 2244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709003126, "dur": 2463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709005589, "dur": 2892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709008482, "dur": 1322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709009805, "dur": 1777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709011585, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514709012133, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709012461, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752514709013244, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709013838, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709014564, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514709014814, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709014895, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752514709015562, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709015825, "dur": 881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709016707, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709017430, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752514709017641, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709017772, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752514709018569, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709018810, "dur": 156560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709175372, "dur": 2753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752514709178126, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709178263, "dur": 3088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752514709181356, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709181481, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752514709181545, "dur": 3148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752514709184694, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709184853, "dur": 3113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752514709187966, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709188130, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752514709188189, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708927334, "dur": 32597, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708959939, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_B2EE5F0EF5DE90F3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708960530, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708960623, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_696CD13C13F48FD0.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708960683, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708960792, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FC6FAA0113914D0C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708960905, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708961013, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_05E287D9A927010F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708961085, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708961168, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_32878187AF3E880A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708961246, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708961364, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_2AB6436578AD960E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708961473, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708961544, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_2AB6436578AD960E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708961827, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_6F5F8D1F67AD384D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708961902, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708962002, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708962173, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514708962253, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708962522, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708962699, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708962818, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708962942, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963053, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963132, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963214, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963304, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963393, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963471, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752514708963571, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963662, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963779, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963865, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708963947, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708964132, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708964263, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708964378, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708964454, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752514708964783, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708964888, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752514708964987, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708965162, "dur": 2021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708968229, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Actions\\MarkerAction.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752514708967183, "dur": 2513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708969697, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708971331, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708973438, "dur": 2351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708975790, "dur": 2122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708977988, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Attributes\\SRPFilterAttribute.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752514708977913, "dur": 2459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708980373, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708983684, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\OnEnterState.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752514708982248, "dur": 2694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708986750, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Time\\WaitUnit.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752514708984943, "dur": 2816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708987760, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708989648, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708989747, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708990021, "dur": 1744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708991765, "dur": 1916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708993682, "dur": 1908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708995591, "dur": 2115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514708997706, "dur": 2367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709000073, "dur": 2519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709002998, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Merge\\Gluon\\IncomingChangesTreeView.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752514709002592, "dur": 2360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709004953, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709006598, "dur": 2206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709008805, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709009794, "dur": 1816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709011612, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514709011889, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709012001, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752514709012758, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709012982, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514709013240, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709013471, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752514709014255, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709014508, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709014578, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709014649, "dur": 1135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709015784, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709016708, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709017422, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752514709017612, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709017727, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752514709018314, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709018581, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709018691, "dur": 156462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709175164, "dur": 2773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752514709177938, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709178376, "dur": 3014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752514709181392, "dur": 641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709182040, "dur": 2667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752514709184708, "dur": 1051, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709185768, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709185859, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709186089, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709186164, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709186570, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709186691, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709186832, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709186931, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709187042, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709187114, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709187175, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709187261, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709187358, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709187433, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709188289, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752514709188351, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708927378, "dur": 32580, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708959966, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_76441C90100FA1E6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708960596, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708960720, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_518A9ED6D3A2C6BF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708960790, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708960876, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4612CC3C65EDFE65.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708960943, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708961031, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_52350FE5FF818038.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708961097, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708961179, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7A4C446C0EECCE90.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708961240, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708961414, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_EDCC7C97B534FFC2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708961502, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708961579, "dur": 384, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_EDCC7C97B534FFC2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708961970, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_13CBADAEC89B0C34.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708962053, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708962140, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708962227, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708962297, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708962367, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708962453, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708962543, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708962624, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A5FBD79E52C0F38C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514708962796, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708962925, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963108, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963185, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963290, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963382, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963508, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963598, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752514708963695, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963785, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963865, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708963965, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708964044, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708964125, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708964242, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708964349, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708964421, "dur": 379, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752514708964832, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708965015, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708965095, "dur": 2138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708967234, "dur": 1833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708969068, "dur": 1584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708970652, "dur": 3086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708975692, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Drawing\\Views\\ShaderPort.cs"}}, {"pid": 12345, "tid": 14, "ts": 1752514708973738, "dur": 2527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708977936, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@8d13f365c663\\Editor\\Data\\Nodes\\Input\\Geometry\\BitangentVectorNode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1752514708976265, "dur": 2751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708979017, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708981061, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708983154, "dur": 2603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708985758, "dur": 1979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708987738, "dur": 2828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708990567, "dur": 2339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708992907, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708994871, "dur": 2136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708997008, "dur": 2157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514708999166, "dur": 2969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709002136, "dur": 3011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709005148, "dur": 2681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709007872, "dur": 1902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709009779, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709009870, "dur": 1421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709011298, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514709011505, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709011609, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752514709012504, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709012857, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752514709013109, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709013191, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752514709014014, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709014271, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709014417, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709014595, "dur": 1191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709015787, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709016714, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709017440, "dur": 157700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709175142, "dur": 3233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752514709178376, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709178681, "dur": 2906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752514709181588, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709181819, "dur": 3029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752514709184849, "dur": 781, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709185650, "dur": 2826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752514709188477, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709188642, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752514709188710, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708927417, "dur": 32549, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708959974, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_51BA6721D0989496.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514708960740, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708960821, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3B4F7A443640C99A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514708961002, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708961083, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_99712846E0BBE01D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514708961163, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708961241, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A7DB62CB7F71C39B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514708961368, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708961449, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A7DB62CB7F71C39B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514708961647, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_41D9C6DBC4E40D6F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514708961704, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708961788, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_171AD846FBA3487F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514708961856, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708962128, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514708962215, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708962336, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708962458, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708962557, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708962656, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708962753, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708962861, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963030, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963108, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963189, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963285, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963368, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963488, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963570, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963652, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963767, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963857, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708963945, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708964104, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708964250, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708964329, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708964406, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752514708964593, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708964698, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708964859, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752514708964910, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708965121, "dur": 2020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708967142, "dur": 2535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708969678, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708971603, "dur": 1834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708973437, "dur": 2415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708975853, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708978001, "dur": 2214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708980216, "dur": 2466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708982683, "dur": 2786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708985469, "dur": 2859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708988329, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708990401, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708992259, "dur": 2057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708994316, "dur": 2743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708997059, "dur": 2540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514708999599, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709001395, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709003331, "dur": 2276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709005608, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709005832, "dur": 2048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709007881, "dur": 1895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709009777, "dur": 1488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709011267, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514709011479, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709011587, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752514709014189, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709014428, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709014525, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514709014716, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709014833, "dur": 1541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752514709016375, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709016565, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709016665, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514709016821, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709016886, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752514709018261, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709018533, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709018644, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752514709018825, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709018922, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752514709019394, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709019626, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709019693, "dur": 155458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709175152, "dur": 3005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752514709178158, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709178332, "dur": 5687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752514709184020, "dur": 1374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709185400, "dur": 2890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752514709188291, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752514709188481, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708927462, "dur": 32515, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708959978, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5DE4DE70E1C466A5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514708960771, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708960867, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_2A30439E8ED584C9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514708960937, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708961016, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_2FE33D2F6C313A03.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514708961087, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708961176, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_3B24859D42648FF8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514708961388, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708961533, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_3B24859D42648FF8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514708961793, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_442E51E6724CBE4C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514708961854, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708962012, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708962284, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708962373, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708962457, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708962537, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708962617, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_4A87A716FBA7BB4C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514708962709, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708962813, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708962925, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708963087, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752514708963182, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708963286, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708963374, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708963521, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708963609, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708963970, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708964048, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708964146, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708964306, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708964376, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1752514708964511, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708964626, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708964754, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708964861, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708964931, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752514708965161, "dur": 2035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708967197, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708969242, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708970568, "dur": 2135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708972703, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708974471, "dur": 1993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708976464, "dur": 2527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708978992, "dur": 2185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708981266, "dur": 693, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@c8192b95703a\\Editor\\Material\\MaterialHeaderScope.cs"}}, {"pid": 12345, "tid": 16, "ts": 1752514708981177, "dur": 2439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708983616, "dur": 2798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708986414, "dur": 2330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708988745, "dur": 2779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708991525, "dur": 2170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708993696, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708995606, "dur": 2575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514708998182, "dur": 2519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709000701, "dur": 2418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709003119, "dur": 2170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709005290, "dur": 2637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709007928, "dur": 1859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709009788, "dur": 1497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709011286, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514709011505, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709011673, "dur": 1267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709012941, "dur": 912, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709013866, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709013951, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709014528, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514709014729, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709014819, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709015427, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709015684, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709015791, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709016697, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514709016882, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709016965, "dur": 1595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709018561, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709018878, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709018965, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514709019169, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709019261, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709020034, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709020223, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709020293, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514709020482, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709020565, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709021332, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709021541, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709021602, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752514709021710, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709021773, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709022118, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709022279, "dur": 152863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709175146, "dur": 2992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709178139, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709178308, "dur": 3517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709181826, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709182411, "dur": 2981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709185393, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709185655, "dur": 3061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752514709188717, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752514709188932, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752514709202105, "dur": 5747, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 13644, "tid": 11, "ts": 1752514709233599, "dur": 2474, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 13644, "tid": 11, "ts": 1752514709236159, "dur": 2942, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 13644, "tid": 11, "ts": 1752514709226644, "dur": 13655, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}