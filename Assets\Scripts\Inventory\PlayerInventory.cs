using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Manages the player's inventory system
/// </summary>
public class PlayerInventory : MonoBehaviour
{
    [Header("Inventory Settings")]
    [SerializeField] private int inventorySize = 30;
    [SerializeField] private float maxWeight = 100f;
    [SerializeField] private bool unlimitedWeight = false;
    
    [Header("Quick Slots")]
    [SerializeField] private int quickSlotCount = 6;
    [SerializeField] private KeyCode[] quickSlotKeys = 
    {
        KeyCode.Alpha1, KeyCode.Alpha2, KeyCode.Alpha3,
        KeyCode.Alpha4, KeyCode.Alpha5, KeyCode.Alpha6
    };
    
    [Header("Audio")]
    [SerializeField] private AudioClip itemPickupSound;
    [SerializeField] private AudioClip itemDropSound;
    [SerializeField] private AudioClip inventoryFullSound;
    
    // Private fields
    private List<ItemStack> inventory = new List<ItemStack>();
    private ItemStack[] quickSlots;
    private AudioSource audioSource;
    
    // Properties
    public int InventorySize => inventorySize;
    public float CurrentWeight => inventory.Sum(stack => stack?.TotalWeight ?? 0f);
    public float MaxWeight => maxWeight;
    public bool IsInventoryFull => inventory.Count >= inventorySize;
    public bool IsOverweight => !unlimitedWeight && CurrentWeight > maxWeight;
    public List<ItemStack> Items => inventory.ToList(); // Return copy to prevent external modification
    
    // Events
    public System.Action OnInventoryChanged;
    public System.Action<int> OnQuickSlotChanged;
    
    private void Start()
    {
        InitializeInventory();
    }
    
    private void Update()
    {
        HandleQuickSlotInput();
    }
    
    private void InitializeInventory()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
            
        // Initialize quick slots
        quickSlots = new ItemStack[quickSlotCount];
        
        // Initialize inventory with empty slots
        inventory = new List<ItemStack>();
    }
    
    private void HandleQuickSlotInput()
    {
        for (int i = 0; i < quickSlotKeys.Length && i < quickSlotCount; i++)
        {
            if (Input.GetKeyDown(quickSlotKeys[i]))
            {
                UseQuickSlot(i);
            }
        }
    }
    
    /// <summary>
    /// Add an item to the inventory
    /// </summary>
    public bool AddItem(Item item, int quantity = 1)
    {
        if (item == null || quantity <= 0) return false;
        
        // Check weight limit
        if (!unlimitedWeight && CurrentWeight + (item.weight * quantity) > maxWeight)
        {
            PlaySound(inventoryFullSound);
            return false;
        }
        
        int remainingQuantity = quantity;
        
        // Try to stack with existing items
        foreach (ItemStack stack in inventory)
        {
            if (stack.CanStackWith(item))
            {
                remainingQuantity = stack.AddToStack(remainingQuantity);
                if (remainingQuantity <= 0) break;
            }
        }
        
        // Create new stacks for remaining quantity
        while (remainingQuantity > 0 && inventory.Count < inventorySize)
        {
            int stackSize = Mathf.Min(remainingQuantity, item.maxStackSize);
            inventory.Add(new ItemStack(item, stackSize));
            remainingQuantity -= stackSize;
        }
        
        // Check if we couldn't add all items
        if (remainingQuantity > 0)
        {
            PlaySound(inventoryFullSound);
            return false;
        }
        
        PlaySound(itemPickupSound);
        OnInventoryChanged?.Invoke();
        return true;
    }
    
    /// <summary>
    /// Remove an item from the inventory
    /// </summary>
    public bool RemoveItem(Item item, int quantity = 1)
    {
        if (item == null || quantity <= 0) return false;
        
        int remainingToRemove = quantity;
        
        for (int i = inventory.Count - 1; i >= 0; i--)
        {
            ItemStack stack = inventory[i];
            if (stack.item == item)
            {
                int removeFromStack = Mathf.Min(remainingToRemove, stack.quantity);
                stack.quantity -= removeFromStack;
                remainingToRemove -= removeFromStack;
                
                if (stack.IsEmpty)
                {
                    inventory.RemoveAt(i);
                }
                
                if (remainingToRemove <= 0) break;
            }
        }
        
        if (remainingToRemove < quantity)
        {
            OnInventoryChanged?.Invoke();
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// Get the quantity of a specific item in inventory
    /// </summary>
    public int GetItemCount(Item item)
    {
        return inventory.Where(stack => stack.item == item).Sum(stack => stack.quantity);
    }
    
    /// <summary>
    /// Check if inventory contains a specific item
    /// </summary>
    public bool HasItem(Item item, int quantity = 1)
    {
        return GetItemCount(item) >= quantity;
    }
    
    /// <summary>
    /// Use an item from inventory
    /// </summary>
    public bool UseItem(Item item, int quantity = 1)
    {
        if (!HasItem(item, quantity)) return false;
        
        bool success = false;
        for (int i = 0; i < quantity; i++)
        {
            if (item.Use(gameObject))
            {
                RemoveItem(item, 1);
                success = true;
            }
            else
            {
                break;
            }
        }
        
        return success;
    }
    
    /// <summary>
    /// Drop an item from inventory into the world
    /// </summary>
    public void DropItem(Item item, int quantity = 1)
    {
        if (!HasItem(item, quantity)) return;
        
        RemoveItem(item, quantity);
        
        // Create world item
        GameObject worldItemPrefab = Resources.Load<GameObject>("Prefabs/WorldItem");
        if (worldItemPrefab != null)
        {
            Vector3 dropPosition = transform.position + transform.forward * 2f;
            GameObject droppedItem = Instantiate(worldItemPrefab, dropPosition, Quaternion.identity);
            
            WorldItem worldItem = droppedItem.GetComponent<WorldItem>();
            if (worldItem != null)
            {
                worldItem.SetItem(item, quantity);
            }
        }
        
        PlaySound(itemDropSound);
    }
    
    /// <summary>
    /// Set item in quick slot
    /// </summary>
    public void SetQuickSlot(int slotIndex, ItemStack itemStack)
    {
        if (slotIndex >= 0 && slotIndex < quickSlotCount)
        {
            quickSlots[slotIndex] = itemStack;
            OnQuickSlotChanged?.Invoke(slotIndex);
        }
    }
    
    /// <summary>
    /// Get item from quick slot
    /// </summary>
    public ItemStack GetQuickSlot(int slotIndex)
    {
        if (slotIndex >= 0 && slotIndex < quickSlotCount)
            return quickSlots[slotIndex];
        return null;
    }
    
    /// <summary>
    /// Use item from quick slot
    /// </summary>
    public void UseQuickSlot(int slotIndex)
    {
        ItemStack quickSlot = GetQuickSlot(slotIndex);
        if (quickSlot != null && quickSlot.item != null)
        {
            UseItem(quickSlot.item, 1);
        }
    }
    
    /// <summary>
    /// Clear all items from inventory
    /// </summary>
    public void ClearInventory()
    {
        inventory.Clear();
        quickSlots = new ItemStack[quickSlotCount];
        OnInventoryChanged?.Invoke();
    }
    
    /// <summary>
    /// Get inventory data for saving
    /// </summary>
    public InventorySaveData GetSaveData()
    {
        InventorySaveData saveData = new InventorySaveData();
        saveData.items = new List<ItemSaveData>();
        
        foreach (ItemStack stack in inventory)
        {
            saveData.items.Add(new ItemSaveData
            {
                itemName = stack.item.name,
                quantity = stack.quantity,
                durability = stack.durability
            });
        }
        
        return saveData;
    }
    
    /// <summary>
    /// Load inventory data
    /// </summary>
    public void LoadSaveData(InventorySaveData saveData)
    {
        ClearInventory();
        
        foreach (ItemSaveData itemData in saveData.items)
        {
            Item item = Resources.Load<Item>($"Items/{itemData.itemName}");
            if (item != null)
            {
                ItemStack stack = new ItemStack(item, itemData.quantity);
                stack.durability = itemData.durability;
                inventory.Add(stack);
            }
        }
        
        OnInventoryChanged?.Invoke();
    }
    
    private void PlaySound(AudioClip clip)
    {
        if (clip != null && audioSource != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
}

[System.Serializable]
public class InventorySaveData
{
    public List<ItemSaveData> items = new List<ItemSaveData>();
}

[System.Serializable]
public class ItemSaveData
{
    public string itemName;
    public int quantity;
    public float durability;
}
