using UnityEngine;

/// <summary>
/// Manual game setup - attach this to a GameObject in the scene
/// </summary>
public class ManualGameSetup : MonoBehaviour
{
    [Header("Auto Setup")]
    [SerializeField] private bool setupOnStart = true;
    
    private void Start()
    {
        if (setupOnStart)
        {
            Debug.Log("=== MANUAL SPACE GAME SETUP STARTING ===");
            SetupCompleteGame();
        }
    }
    
    [ContextMenu("Setup Game Now")]
    public void SetupCompleteGame()
    {
        Debug.Log("Creating space game...");
        
        // Create player first
        CreatePlayer();
        
        // Create environment
        CreateEnvironment();
        
        // Create interactables
        CreateInteractables();
        
        // Create UI
        CreateSimpleUI();
        
        Debug.Log("=== SPACE GAME READY! ===");
        Debug.Log("WASD: Move | Mouse: Look | F: Flashlight | E: Interact");
    }
    
    private void CreatePlayer()
    {
        // Check if player already exists
        if (GameObject.FindGameObjectWithTag("Player") != null)
        {
            Debug.Log("Player already exists");
            return;
        }
        
        // Create player GameObject
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        
        // Add CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        controller.center = new Vector3(0, 1, 0);
        
        // Add basic movement script
        SimplePlayerController playerScript = player.AddComponent<SimplePlayerController>();
        
        // Create camera
        GameObject cameraObj = new GameObject("PlayerCamera");
        cameraObj.transform.SetParent(player.transform);
        cameraObj.transform.localPosition = new Vector3(0, 1.6f, 0);
        
        Camera playerCam = cameraObj.AddComponent<Camera>();
        playerCam.fieldOfView = 75f;
        cameraObj.AddComponent<AudioListener>();
        
        // Set camera reference
        playerScript.playerCamera = playerCam;
        
        Debug.Log("Created player with camera");
    }
    
    private void CreateEnvironment()
    {
        // Create floor
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.name = "Floor";
        floor.transform.localScale = new Vector3(10, 1, 10);
        floor.transform.position = new Vector3(0, 0, 0);
        
        // Create walls
        CreateWall("Wall_North", new Vector3(0, 2.5f, 10), new Vector3(20, 5, 1));
        CreateWall("Wall_South", new Vector3(0, 2.5f, -10), new Vector3(20, 5, 1));
        CreateWall("Wall_East", new Vector3(10, 2.5f, 0), new Vector3(1, 5, 20));
        CreateWall("Wall_West", new Vector3(-10, 2.5f, 0), new Vector3(1, 5, 20));
        
        Debug.Log("Created environment");
    }
    
    private void CreateWall(string name, Vector3 position, Vector3 scale)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = name;
        wall.transform.position = position;
        wall.transform.localScale = scale;
        
        // Make walls gray
        wall.GetComponent<Renderer>().material.color = Color.gray;
    }
    
    private void CreateInteractables()
    {
        // Create a simple door
        GameObject door = GameObject.CreatePrimitive(PrimitiveType.Cube);
        door.name = "SimpleDoor";
        door.transform.position = new Vector3(0, 1.5f, 9.5f);
        door.transform.localScale = new Vector3(2, 3, 0.2f);
        door.GetComponent<Renderer>().material.color = Color.blue;
        door.AddComponent<SimpleDoor>();
        
        // Create upgrade station
        GameObject station = GameObject.CreatePrimitive(PrimitiveType.Cube);
        station.name = "UpgradeStation";
        station.transform.position = new Vector3(5, 1, 0);
        station.transform.localScale = new Vector3(1.5f, 2, 1);
        station.GetComponent<Renderer>().material.color = Color.green;
        station.AddComponent<SimpleUpgradeStation>();
        
        // Create test objects
        GameObject testCube = GameObject.CreatePrimitive(PrimitiveType.Cube);
        testCube.name = "TestCube";
        testCube.transform.position = new Vector3(-3, 0.5f, 0);
        testCube.GetComponent<Renderer>().material.color = Color.red;
        
        Debug.Log("Created interactable objects");
    }
    
    private void CreateSimpleUI()
    {
        // Create Canvas
        GameObject canvasObj = new GameObject("GameCanvas");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
        canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
        
        // Create crosshair
        GameObject crosshair = new GameObject("Crosshair");
        crosshair.transform.SetParent(canvas.transform, false);
        
        UnityEngine.UI.Image crosshairImage = crosshair.AddComponent<UnityEngine.UI.Image>();
        crosshairImage.color = Color.white;
        
        RectTransform crosshairRect = crosshair.GetComponent<RectTransform>();
        crosshairRect.anchorMin = new Vector2(0.5f, 0.5f);
        crosshairRect.anchorMax = new Vector2(0.5f, 0.5f);
        crosshairRect.anchoredPosition = Vector2.zero;
        crosshairRect.sizeDelta = new Vector2(20, 20);
        
        // Create instructions
        GameObject instructionText = new GameObject("Instructions");
        instructionText.transform.SetParent(canvas.transform, false);
        
        UnityEngine.UI.Text text = instructionText.AddComponent<UnityEngine.UI.Text>();
        text.text = "WASD: Move | Mouse: Look | E: Interact | Blue=Door | Green=Upgrade Station";
        text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        text.fontSize = 16;
        text.color = Color.white;
        text.alignment = TextAnchor.UpperLeft;
        
        RectTransform textRect = instructionText.GetComponent<RectTransform>();
        textRect.anchorMin = new Vector2(0, 1);
        textRect.anchorMax = new Vector2(1, 1);
        textRect.anchoredPosition = new Vector2(0, -20);
        textRect.sizeDelta = new Vector2(0, 30);
        
        Debug.Log("Created UI");
    }
}

/// <summary>
/// Simple player controller that definitely works
/// </summary>
public class SimplePlayerController : MonoBehaviour
{
    public Camera playerCamera;
    public float moveSpeed = 5f;
    public float mouseSensitivity = 2f;
    
    private CharacterController controller;
    private float xRotation = 0f;
    
    private void Start()
    {
        controller = GetComponent<CharacterController>();
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }
    
    private void Update()
    {
        HandleMovement();
        HandleMouseLook();
        HandleInteraction();
    }
    
    private void HandleMovement()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        Vector3 direction = transform.right * horizontal + transform.forward * vertical;
        controller.Move(direction * moveSpeed * Time.deltaTime);
        
        // Simple gravity
        controller.Move(Vector3.down * 9.81f * Time.deltaTime);
    }
    
    private void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
        
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -90f, 90f);
        
        playerCamera.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        transform.Rotate(Vector3.up * mouseX);
    }
    
    private void HandleInteraction()
    {
        if (Input.GetKeyDown(KeyCode.E))
        {
            RaycastHit hit;
            if (Physics.Raycast(playerCamera.transform.position, playerCamera.transform.forward, out hit, 5f))
            {
                SimpleDoor door = hit.collider.GetComponent<SimpleDoor>();
                if (door != null)
                {
                    door.ToggleDoor();
                }
                
                SimpleUpgradeStation station = hit.collider.GetComponent<SimpleUpgradeStation>();
                if (station != null)
                {
                    station.UseStation();
                }
            }
        }
    }
}

/// <summary>
/// Simple door that works
/// </summary>
public class SimpleDoor : MonoBehaviour
{
    private bool isOpen = false;
    private Vector3 closedPosition;
    private Vector3 openPosition;
    
    private void Start()
    {
        closedPosition = transform.position;
        openPosition = closedPosition + Vector3.up * 3f;
    }
    
    public void ToggleDoor()
    {
        isOpen = !isOpen;
        transform.position = isOpen ? openPosition : closedPosition;
        Debug.Log($"Door {(isOpen ? "opened" : "closed")}");
    }
}

/// <summary>
/// Simple upgrade station
/// </summary>
public class SimpleUpgradeStation : MonoBehaviour
{
    public void UseStation()
    {
        Debug.Log("Used upgrade station! (Upgrade system would open here)");
    }
}
