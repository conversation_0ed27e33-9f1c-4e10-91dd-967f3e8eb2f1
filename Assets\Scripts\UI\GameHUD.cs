using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Main HUD for the space game showing player status and information
/// </summary>
public class GameHUD : MonoBehaviour
{
    [Header("Resource Bars")]
    [SerializeField] private Slider healthBar;
    [SerializeField] private Slider hungerBar;
    [SerializeField] private Slider oxygenBar;
    [SerializeField] private Slider energyBar;
    
    [Header("Resource Text")]
    [SerializeField] private TextMeshProUGUI healthText;
    [SerializeField] private TextMeshProUGUI hungerText;
    [SerializeField] private TextMeshProUGUI oxygenText;
    [SerializeField] private TextMeshProUGUI energyText;
    
    [Header("Resource Icons")]
    [SerializeField] private Image healthIcon;
    [SerializeField] private Image hungerIcon;
    [SerializeField] private Image oxygenIcon;
    [SerializeField] private Image energyIcon;
    
    [Header("Interaction")]
    [SerializeField] private GameObject interactionPanel;
    [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> interactionText;
    [SerializeField] private Image interactionIcon;
    
    [Header("Crosshair")]
    [SerializeField] private GameObject crosshair;
    [SerializeField] private Image crosshairImage;
    [SerializeField] private Color normalCrosshairColor = Color.white;
    [SerializeField] private Color interactableCrosshairColor = Color.green;
    
    [Header("Quick Slots")]
    [SerializeField] private Transform quickSlotsParent;
    [SerializeField] private GameObject quickSlotPrefab;
    [SerializeField] private QuickSlotUI[] quickSlots;
    
    [Header("Status Effects")]
    [SerializeField] private Transform statusEffectsParent;
    [SerializeField] private GameObject statusEffectPrefab;
    
    [Header("Notifications")]
    [SerializeField] private Transform notificationParent;
    [SerializeField] private GameObject notificationPrefab;
    [SerializeField] private float notificationDuration = 3f;
    
    private PlayerResources playerResources;
    private PlayerInventory playerInventory;
    private PlayerController playerController;
    
    private void Start()
    {
        InitializeHUD();
        SubscribeToEvents();
    }
    
    private void Update()
    {
        UpdateHUD();
    }
    
    private void InitializeHUD()
    {
        // Find player components
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            playerResources = player.GetComponent<PlayerResources>();
            playerInventory = player.GetComponent<PlayerInventory>();
            playerController = player.GetComponent<PlayerController>();
        }
        
        // Initialize quick slots
        InitializeQuickSlots();
        
        // Hide interaction panel initially
        if (interactionPanel != null)
            interactionPanel.SetActive(false);
    }
    
    private void InitializeQuickSlots()
    {
        if (quickSlotsParent == null || quickSlotPrefab == null) return;
        
        // Create quick slot UI elements
        int quickSlotCount = playerInventory != null ? 6 : 6; // Default to 6
        quickSlots = new QuickSlotUI[quickSlotCount];
        
        for (int i = 0; i < quickSlotCount; i++)
        {
            GameObject slotObj = Instantiate(quickSlotPrefab, quickSlotsParent);
            QuickSlotUI slotUI = slotObj.GetComponent<QuickSlotUI>();
            if (slotUI != null)
            {
                slotUI.Initialize(i);
                quickSlots[i] = slotUI;
            }
        }
    }
    
    private void SubscribeToEvents()
    {
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Subscribe<PlayerHealthChangedEvent>(OnHealthChanged);
            EventManager.Instance.Subscribe<PlayerHungerChangedEvent>(OnHungerChanged);
            EventManager.Instance.Subscribe<PlayerOxygenChangedEvent>(OnOxygenChanged);
            EventManager.Instance.Subscribe<ItemPickedUpEvent>(OnItemPickedUp);
        }
        
        if (playerInventory != null)
        {
            playerInventory.OnInventoryChanged += UpdateQuickSlots;
            playerInventory.OnQuickSlotChanged += UpdateQuickSlot;
        }
    }
    
    private void UpdateHUD()
    {
        UpdateResourceBars();
        UpdateInteraction();
        UpdateCrosshair();
    }
    
    private void UpdateResourceBars()
    {
        if (playerResources == null) return;
        
        // Update health
        if (healthBar != null)
            healthBar.value = playerResources.HealthPercentage;
        if (healthText != null)
            healthText.text = $"{(int)(playerResources.HealthPercentage * 100)}%";
        
        // Update hunger
        if (hungerBar != null)
            hungerBar.value = playerResources.HungerPercentage;
        if (hungerText != null)
            hungerText.text = $"{(int)(playerResources.HungerPercentage * 100)}%";
        
        // Update oxygen
        if (oxygenBar != null)
            oxygenBar.value = playerResources.OxygenPercentage;
        if (oxygenText != null)
            oxygenText.text = $"{(int)(playerResources.OxygenPercentage * 100)}%";
        
        // Update energy
        if (energyBar != null)
            energyBar.value = playerResources.EnergyPercentage;
        if (energyText != null)
            energyText.text = $"{(int)(playerResources.EnergyPercentage * 100)}%";
        
        // Update resource bar colors based on status
        UpdateResourceBarColors();
    }
    
    private void UpdateResourceBarColors()
    {
        if (playerResources == null) return;
        
        // Health bar color
        if (healthBar != null)
        {
            Color healthColor = Color.green;
            if (playerResources.IsHealthCritical)
                healthColor = Color.red;
            else if (playerResources.IsHealthLow)
                healthColor = Color.yellow;
                
            healthBar.fillRect.GetComponent<Image>().color = healthColor;
        }
        
        // Oxygen bar color
        if (oxygenBar != null)
        {
            Color oxygenColor = playerResources.IsOutOfOxygen ? Color.red : Color.cyan;
            oxygenBar.fillRect.GetComponent<Image>().color = oxygenColor;
        }
        
        // Hunger bar color
        if (hungerBar != null)
        {
            Color hungerColor = playerResources.IsHungry ? Color.red : Color.orange;
            hungerBar.fillRect.GetComponent<Image>().color = hungerColor;
        }
        
        // Energy bar color
        if (energyBar != null)
        {
            Color energyColor = playerResources.IsExhausted ? Color.red : Color.blue;
            energyBar.fillRect.GetComponent<Image>().color = energyColor;
        }
    }
    
    private void UpdateInteraction()
    {
        if (playerController == null || interactionPanel == null) return;
        
        IInteractable currentInteractable = playerController.CurrentInteractable;
        
        if (currentInteractable != null && currentInteractable.CanInteract)
        {
            interactionPanel.SetActive(true);
            if (interactionText != null)
                interactionText.text = $"[E] {currentInteractable.InteractionText}";
        }
        else
        {
            interactionPanel.SetActive(false);
        }
    }
    
    private void UpdateCrosshair()
    {
        if (crosshairImage == null || playerController == null) return;
        
        bool hasInteractable = playerController.CurrentInteractable != null;
        crosshairImage.color = hasInteractable ? interactableCrosshairColor : normalCrosshairColor;
    }
    
    private void UpdateQuickSlots()
    {
        if (quickSlots == null || playerInventory == null) return;
        
        for (int i = 0; i < quickSlots.Length; i++)
        {
            if (quickSlots[i] != null)
            {
                ItemStack quickSlot = playerInventory.GetQuickSlot(i);
                quickSlots[i].UpdateSlot(quickSlot);
            }
        }
    }
    
    private void UpdateQuickSlot(int slotIndex)
    {
        if (quickSlots != null && slotIndex >= 0 && slotIndex < quickSlots.Length)
        {
            ItemStack quickSlot = playerInventory.GetQuickSlot(slotIndex);
            quickSlots[slotIndex].UpdateSlot(quickSlot);
        }
    }
    
    public void ShowNotification(string message, Color color = default)
    {
        if (notificationParent == null || notificationPrefab == null) return;
        
        GameObject notificationObj = Instantiate(notificationPrefab, notificationParent);
        NotificationUI notification = notificationObj.GetComponent<NotificationUI>();
        
        if (notification != null)
        {
            if (color == default) color = Color.white;
            notification.ShowNotification(message, color, notificationDuration);
        }
    }
    
    public void SetCrosshairVisible(bool visible)
    {
        if (crosshair != null)
            crosshair.SetActive(visible);
    }
    
    // Event handlers
    private void OnHealthChanged(PlayerHealthChangedEvent healthEvent)
    {
        // Health bar will be updated in UpdateResourceBars
        
        if (healthEvent.currentHealth < healthEvent.previousHealth)
        {
            // Show damage notification
            float damage = healthEvent.previousHealth - healthEvent.currentHealth;
            ShowNotification($"-{damage:F0} Health", Color.red);
        }
    }
    
    private void OnHungerChanged(PlayerHungerChangedEvent hungerEvent)
    {
        // Hunger bar will be updated in UpdateResourceBars
    }
    
    private void OnOxygenChanged(PlayerOxygenChangedEvent oxygenEvent)
    {
        // Oxygen bar will be updated in UpdateResourceBars
        
        if (oxygenEvent.currentOxygen <= 0f && oxygenEvent.previousOxygen > 0f)
        {
            ShowNotification("WARNING: Out of Oxygen!", Color.red);
        }
    }
    
    private void OnItemPickedUp(ItemPickedUpEvent itemEvent)
    {
        ShowNotification($"Picked up {itemEvent.itemName} x{itemEvent.quantity}", Color.green);
    }
    
    private void OnDestroy()
    {
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Unsubscribe<PlayerHealthChangedEvent>(OnHealthChanged);
            EventManager.Instance.Unsubscribe<PlayerHungerChangedEvent>(OnHungerChanged);
            EventManager.Instance.Unsubscribe<PlayerOxygenChangedEvent>(OnOxygenChanged);
            EventManager.Instance.Unsubscribe<ItemPickedUpEvent>(OnItemPickedUp);
        }
        
        if (playerInventory != null)
        {
            playerInventory.OnInventoryChanged -= UpdateQuickSlots;
            playerInventory.OnQuickSlotChanged -= UpdateQuickSlot;
        }
    }
}

/// <summary>
/// UI component for quick slot display
/// </summary>
public class QuickSlotUI : MonoBehaviour
{
    [Header("UI Elements")]
    [SerializeField] private Image itemIcon;
    [SerializeField] private TextMeshProUGUI quantityText;
    [SerializeField] private TextMeshProUGUI keyText;
    [SerializeField] private Image backgroundImage;

    [Header("Colors")]
    [SerializeField] private Color normalColor = Color.white;
    [SerializeField] private Color emptyColor = Color.gray;
    [SerializeField] private Color selectedColor = Color.yellow;

    private int slotIndex;
    private ItemStack currentItem;

    public void Initialize(int index)
    {
        slotIndex = index;

        // Set key text (1-6)
        if (keyText != null)
            keyText.text = (index + 1).ToString();

        UpdateSlot(null);
    }

    public void UpdateSlot(ItemStack itemStack)
    {
        currentItem = itemStack;

        if (itemStack != null && itemStack.item != null)
        {
            // Show item
            if (itemIcon != null)
            {
                itemIcon.sprite = itemStack.item.icon;
                itemIcon.color = normalColor;
            }

            if (quantityText != null)
            {
                quantityText.text = itemStack.quantity > 1 ? itemStack.quantity.ToString() : "";
                quantityText.gameObject.SetActive(itemStack.quantity > 1);
            }

            if (backgroundImage != null)
                backgroundImage.color = normalColor;
        }
        else
        {
            // Empty slot
            if (itemIcon != null)
            {
                itemIcon.sprite = null;
                itemIcon.color = emptyColor;
            }

            if (quantityText != null)
                quantityText.gameObject.SetActive(false);

            if (backgroundImage != null)
                backgroundImage.color = emptyColor;
        }
    }

    public void SetSelected(bool selected)
    {
        if (backgroundImage != null)
        {
            backgroundImage.color = selected ? selectedColor :
                (currentItem != null ? normalColor : emptyColor);
        }
    }
}

/// <summary>
/// UI component for notifications
/// </summary>
public class NotificationUI : MonoBehaviour
{
    [Header("UI Elements")]
    [SerializeField] private TextMeshProUGUI messageText;
    [SerializeField] private Image backgroundImage;

    [Header("Animation")]
    [SerializeField] private float fadeInDuration = 0.3f;
    [SerializeField] private float fadeOutDuration = 0.5f;

    private CanvasGroup canvasGroup;

    private void Awake()
    {
        canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = gameObject.AddComponent<CanvasGroup>();
    }

    public void ShowNotification(string message, Color color, float duration)
    {
        if (messageText != null)
        {
            messageText.text = message;
            messageText.color = color;
        }

        StartCoroutine(NotificationSequence(duration));
    }

    private System.Collections.IEnumerator NotificationSequence(float duration)
    {
        // Fade in
        canvasGroup.alpha = 0f;
        float elapsed = 0f;

        while (elapsed < fadeInDuration)
        {
            elapsed += Time.deltaTime;
            canvasGroup.alpha = elapsed / fadeInDuration;
            yield return null;
        }

        canvasGroup.alpha = 1f;

        // Wait
        yield return new WaitForSeconds(duration);

        // Fade out
        elapsed = 0f;
        while (elapsed < fadeOutDuration)
        {
            elapsed += Time.deltaTime;
            canvasGroup.alpha = 1f - (elapsed / fadeOutDuration);
            yield return null;
        }

        // Destroy
        Destroy(gameObject);
    }
}
