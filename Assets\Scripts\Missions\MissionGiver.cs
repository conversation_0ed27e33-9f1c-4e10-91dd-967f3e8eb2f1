using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// NPC or object that can give missions to the player
/// </summary>
public class MissionGiver : InteractableBase
{
    [Header("Mission Giver Settings")]
    [SerializeField] private List<string> missionsToGive = new List<string>();
    [SerializeField] private bool giveAllMissions = false;
    [SerializeField] private bool oneTimeOnly = false;
    [SerializeField] private string npcName = "Mission Officer";
    
    [<PERSON><PERSON>("Visual Indicators")]
    [SerializeField] private GameObject missionAvailableIndicator;
    [SerializeField] private GameObject missionCompleteIndicator;
    [SerializeField] private Color availableColor = Color.yellow;
    [SerializeField] private Color completeColor = Color.green;
    
    private List<string> givenMissions = new List<string>();
    private bool hasInteracted = false;
    
    protected override void Awake()
    {
        base.Awake();
        UpdateVisualIndicators();
    }
    
    private void Start()
    {
        if (MissionSystem.Instance != null)
        {
            MissionSystem.Instance.OnMissionCompleted += OnMissionCompleted;
        }
        
        UpdateInteractionText();
    }
    
    private void Update()
    {
        UpdateVisualIndicators();
    }
    
    public override void Interact(GameObject player)
    {
        if (oneTimeOnly && hasInteracted)
        {
            Debug.Log($"{npcName}: I have no more missions for you.");
            return;
        }
        
        List<string> availableMissions = GetAvailableMissions();
        
        if (availableMissions.Count > 0)
        {
            if (giveAllMissions)
            {
                // Give all available missions
                foreach (string missionId in availableMissions)
                {
                    GiveMission(missionId);
                }
            }
            else
            {
                // Give first available mission
                GiveMission(availableMissions[0]);
            }
            
            hasInteracted = true;
        }
        else
        {
            // Check for completed missions to turn in
            List<string> completedMissions = GetCompletedMissions();
            if (completedMissions.Count > 0)
            {
                Debug.Log($"{npcName}: Well done! You've completed your missions.");
                PlayInteractionSound();
            }
            else
            {
                Debug.Log($"{npcName}: I have no missions available for you right now.");
            }
        }
        
        UpdateInteractionText();
    }
    
    public override void OnInteractionEnter(GameObject player)
    {
        base.OnInteractionEnter(player);
        UpdateInteractionText();
    }
    
    private void GiveMission(string missionId)
    {
        if (MissionSystem.Instance == null) return;
        
        MissionSystem.Instance.StartMission(missionId);
        givenMissions.Add(missionId);
        
        Mission mission = MissionSystem.Instance.GetMission(missionId);
        if (mission != null)
        {
            Debug.Log($"{npcName}: {mission.description}");
        }
        
        PlayInteractionSound();
    }
    
    private List<string> GetAvailableMissions()
    {
        List<string> available = new List<string>();
        
        if (MissionSystem.Instance == null) return available;
        
        foreach (string missionId in missionsToGive)
        {
            if (!givenMissions.Contains(missionId) && 
                !MissionSystem.Instance.IsMissionActive(missionId) && 
                !MissionSystem.Instance.IsMissionCompleted(missionId))
            {
                Mission mission = MissionSystem.Instance.GetMission(missionId);
                if (mission != null)
                {
                    available.Add(missionId);
                }
            }
        }
        
        return available;
    }
    
    private List<string> GetCompletedMissions()
    {
        List<string> completed = new List<string>();
        
        if (MissionSystem.Instance == null) return completed;
        
        foreach (string missionId in givenMissions)
        {
            if (MissionSystem.Instance.IsMissionCompleted(missionId))
            {
                completed.Add(missionId);
            }
        }
        
        return completed;
    }
    
    private void UpdateInteractionText()
    {
        List<string> availableMissions = GetAvailableMissions();
        List<string> completedMissions = GetCompletedMissions();
        
        if (availableMissions.Count > 0)
        {
            interactionText = $"Talk to {npcName} (New Mission)";
        }
        else if (completedMissions.Count > 0)
        {
            interactionText = $"Talk to {npcName} (Turn In)";
        }
        else if (oneTimeOnly && hasInteracted)
        {
            interactionText = $"Talk to {npcName}";
            canInteract = false;
        }
        else
        {
            interactionText = $"Talk to {npcName}";
        }
    }
    
    private void UpdateVisualIndicators()
    {
        List<string> availableMissions = GetAvailableMissions();
        List<string> completedMissions = GetCompletedMissions();
        
        // Show mission available indicator
        if (missionAvailableIndicator != null)
        {
            missionAvailableIndicator.SetActive(availableMissions.Count > 0);
        }
        
        // Show mission complete indicator
        if (missionCompleteIndicator != null)
        {
            missionCompleteIndicator.SetActive(completedMissions.Count > 0);
        }
    }
    
    private void OnMissionCompleted(Mission mission)
    {
        if (givenMissions.Contains(mission.missionId))
        {
            Debug.Log($"{npcName}: Excellent work on completing '{mission.missionName}'!");
        }
    }
    
    public void AddMission(string missionId)
    {
        if (!missionsToGive.Contains(missionId))
        {
            missionsToGive.Add(missionId);
        }
    }
    
    public void RemoveMission(string missionId)
    {
        missionsToGive.Remove(missionId);
    }
    
    private void OnDestroy()
    {
        if (MissionSystem.Instance != null)
        {
            MissionSystem.Instance.OnMissionCompleted -= OnMissionCompleted;
        }
    }
}

/// <summary>
/// Simple mission terminal for automated mission distribution
/// </summary>
public class MissionTerminal : InteractableBase
{
    [Header("Terminal Settings")]
    [SerializeField] private List<Mission> terminalMissions = new List<Mission>();
    [SerializeField] private bool isPowered = true;
    
    [Header("Visual Effects")]
    [SerializeField] private Light terminalLight;
    [SerializeField] private Color poweredColor = Color.blue;
    [SerializeField] private Color unpoweredColor = Color.red;
    
    protected override void Awake()
    {
        base.Awake();
        UpdateVisuals();
    }
    
    public override void Interact(GameObject player)
    {
        if (!isPowered)
        {
            Debug.Log("Mission Terminal: No power");
            return;
        }
        
        // Show available missions
        ShowMissionList();
        PlayInteractionSound();
    }
    
    private void ShowMissionList()
    {
        Debug.Log("=== MISSION TERMINAL ===");
        
        if (MissionSystem.Instance == null)
        {
            Debug.Log("Mission system not available");
            return;
        }
        
        List<Mission> availableMissions = MissionSystem.Instance.GetAvailableMissions();
        
        if (availableMissions.Count == 0)
        {
            Debug.Log("No missions available");
            return;
        }
        
        foreach (Mission mission in availableMissions)
        {
            Debug.Log($"- {mission.missionName}: {mission.description}");
        }
        
        // Auto-start first available mission for demo
        if (availableMissions.Count > 0)
        {
            MissionSystem.Instance.StartMission(availableMissions[0].missionId);
        }
    }
    
    private void UpdateVisuals()
    {
        if (terminalLight != null)
        {
            terminalLight.color = isPowered ? poweredColor : unpoweredColor;
            terminalLight.enabled = isPowered;
        }
        
        interactionText = isPowered ? "Use Mission Terminal" : "Mission Terminal (No Power)";
        canInteract = isPowered;
    }
    
    public void SetPowered(bool powered)
    {
        isPowered = powered;
        UpdateVisuals();
    }
}
