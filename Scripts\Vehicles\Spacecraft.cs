using UnityEngine;

/// <summary>
/// Spacecraft vehicle for space exploration and travel
/// </summary>
public class Spacecraft : VehicleBase
{
    [Header("Spacecraft Settings")]
    [SerializeField] private float hoverHeight = 2f;
    [SerializeField] private float hoverForce = 1000f;
    [SerializeField] private float stabilizationForce = 500f;
    [SerializeField] private float liftForce = 1500f;
    [SerializeField] private float maxAltitude = 100f;
    [SerializeField] private bool canLand = true;
    [SerializeField] private LayerMask groundLayerMask = 1;
    
    [Header("Flight Controls")]
    [SerializeField] private float pitchSpeed = 45f;
    [SerializeField] private float rollSpeed = 30f;
    [SerializeField] private float yawSpeed = 60f;
    [SerializeField] private float thrustForce = 2000f;
    
    [Header("Landing")]
    [SerializeField] private Transform[] landingGear;
    [SerializeField] private float landingSpeed = 2f;
    [SerializeField] private float groundCheckDistance = 3f;
    
    private bool isFlying = false;
    private bool isLanded = true;
    private bool isHovering = false;
    private float currentAltitude = 0f;
    private Vector3 targetRotation;
    
    protected override void Awake()
    {
        base.Awake();
        
        // Spacecraft should not be affected by gravity when flying
        if (vehicleRigidbody != null)
        {
            vehicleRigidbody.useGravity = isLanded;
        }
        
        targetRotation = transform.eulerAngles;
    }
    
    protected override void HandlePilotInput()
    {
        base.HandlePilotInput();
        
        if (!isPiloted) return;
        
        // Takeoff/Landing
        if (Input.GetKeyDown(KeyCode.Space))
        {
            if (isLanded)
                TakeOff();
            else if (canLand)
                Land();
        }
        
        // Vertical movement
        if (Input.GetKey(KeyCode.Q))
            moveInput.y = 1f; // Ascend
        else if (Input.GetKey(KeyCode.E))
            moveInput.y = -1f; // Descend
        else
            moveInput.y = 0f;
        
        // Flight controls (when flying)
        if (isFlying)
        {
            // Pitch (mouse Y)
            if (Input.GetKey(KeyCode.LeftShift))
            {
                float mouseY = Input.GetAxis("Mouse Y");
                targetRotation.x -= mouseY * pitchSpeed * Time.deltaTime;
                targetRotation.x = Mathf.Clamp(targetRotation.x, -45f, 45f);
            }
            
            // Roll (Q/E when not ascending/descending)
            if (!Input.GetKey(KeyCode.Q) && !Input.GetKey(KeyCode.E))
            {
                if (Input.GetKey(KeyCode.Q))
                    targetRotation.z += rollSpeed * Time.deltaTime;
                else if (Input.GetKey(KeyCode.E))
                    targetRotation.z -= rollSpeed * Time.deltaTime;
                else
                    targetRotation.z = Mathf.MoveTowards(targetRotation.z, 0f, rollSpeed * Time.deltaTime);
            }
        }
    }
    
    protected override void UpdateMovement()
    {
        if (!isPiloted || !isEngineRunning || !HasFuel) return;
        
        UpdateAltitude();
        
        if (isFlying)
        {
            UpdateFlightMovement();
        }
        else if (isLanded)
        {
            UpdateGroundMovement();
        }
        
        UpdateRotation();
    }
    
    private void UpdateAltitude()
    {
        RaycastHit hit;
        if (Physics.Raycast(transform.position, Vector3.down, out hit, Mathf.Infinity, groundLayerMask))
        {
            currentAltitude = hit.distance;
        }
        else
        {
            currentAltitude = maxAltitude;
        }
    }
    
    private void UpdateFlightMovement()
    {
        // Apply hover force to maintain altitude
        if (isHovering)
        {
            float hoverError = hoverHeight - currentAltitude;
            Vector3 hoverForceVector = Vector3.up * hoverError * hoverForce;
            vehicleRigidbody.AddForce(hoverForceVector);
        }
        
        // Forward/backward movement
        if (Mathf.Abs(moveInput.z) > 0.1f)
        {
            Vector3 thrustDirection = transform.forward * moveInput.z;
            vehicleRigidbody.AddForce(thrustDirection * thrustForce * Time.deltaTime);
        }
        
        // Strafe movement
        if (Mathf.Abs(moveInput.x) > 0.1f)
        {
            Vector3 strafeDirection = transform.right * moveInput.x;
            vehicleRigidbody.AddForce(strafeDirection * thrustForce * 0.5f * Time.deltaTime);
        }
        
        // Vertical movement
        if (Mathf.Abs(moveInput.y) > 0.1f)
        {
            Vector3 verticalDirection = Vector3.up * moveInput.y;
            vehicleRigidbody.AddForce(verticalDirection * liftForce * Time.deltaTime);
        }
        
        // Apply stabilization
        Vector3 stabilization = -vehicleRigidbody.velocity * stabilizationForce * Time.deltaTime;
        stabilization.y *= 0.1f; // Less stabilization on Y axis
        vehicleRigidbody.AddForce(stabilization);
        
        // Limit maximum speed
        if (vehicleRigidbody.velocity.magnitude > maxSpeed)
        {
            vehicleRigidbody.velocity = vehicleRigidbody.velocity.normalized * maxSpeed;
        }
        
        currentSpeed = vehicleRigidbody.velocity.magnitude;
    }
    
    private void UpdateGroundMovement()
    {
        // Simple ground movement (like a rover)
        Vector3 moveDirection = transform.TransformDirection(new Vector3(moveInput.x, 0f, moveInput.z));
        
        if (moveInput.magnitude > 0.1f)
        {
            currentSpeed = Mathf.MoveTowards(currentSpeed, maxSpeed * moveInput.magnitude, acceleration * Time.deltaTime);
        }
        else
        {
            currentSpeed = Mathf.MoveTowards(currentSpeed, 0f, deceleration * Time.deltaTime);
        }
        
        Vector3 velocity = moveDirection.normalized * currentSpeed;
        velocity.y = vehicleRigidbody.velocity.y;
        vehicleRigidbody.velocity = velocity;
        
        // Ground turning
        if (Mathf.Abs(moveInput.x) > 0.1f)
        {
            float turnAmount = moveInput.x * turnSpeed * Time.deltaTime;
            transform.Rotate(0f, turnAmount, 0f);
        }
    }
    
    private void UpdateRotation()
    {
        if (isFlying)
        {
            // Smooth rotation towards target
            Quaternion targetQuat = Quaternion.Euler(targetRotation);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetQuat, Time.deltaTime * 2f);
        }
    }
    
    private void TakeOff()
    {
        if (!isLanded || !HasFuel) return;
        
        isLanded = false;
        isFlying = true;
        isHovering = true;
        
        vehicleRigidbody.useGravity = false;
        
        // Retract landing gear
        SetLandingGear(false);
        
        Debug.Log("Spacecraft taking off");
    }
    
    private void Land()
    {
        if (isLanded || currentAltitude > 5f) return;
        
        isFlying = false;
        isHovering = false;
        
        // Start landing sequence
        StartCoroutine(LandingSequence());
    }
    
    private System.Collections.IEnumerator LandingSequence()
    {
        // Extend landing gear
        SetLandingGear(true);
        
        // Slowly descend
        while (currentAltitude > 0.5f)
        {
            Vector3 landingForce = Vector3.down * landingSpeed;
            vehicleRigidbody.AddForce(landingForce);
            yield return null;
        }
        
        // Landed
        isLanded = true;
        vehicleRigidbody.useGravity = true;
        vehicleRigidbody.velocity = Vector3.zero;
        
        // Reset rotation
        targetRotation = new Vector3(0f, transform.eulerAngles.y, 0f);
        
        Debug.Log("Spacecraft landed");
    }
    
    private void SetLandingGear(bool extended)
    {
        if (landingGear == null) return;
        
        foreach (Transform gear in landingGear)
        {
            if (gear != null)
            {
                gear.gameObject.SetActive(extended);
            }
        }
    }
    
    protected override void UpdateInteractionText()
    {
        if (isPiloted)
        {
            if (isLanded)
                interactionText = "Exit Spacecraft (F) | Takeoff (Space)";
            else
                interactionText = "Exit Spacecraft (F) | Land (Space)";
        }
        else if (!HasFuel)
        {
            interactionText = "Spacecraft (No Fuel)";
        }
        else
        {
            interactionText = "Enter Spacecraft";
        }
    }
    
    // Properties
    public bool IsFlying => isFlying;
    public bool IsLanded => isLanded;
    public float CurrentAltitude => currentAltitude;
    
    private void OnDrawGizmosSelected()
    {
        base.OnDrawGizmosSelected();
        
        // Draw hover height
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(transform.position - Vector3.up * hoverHeight, 1f);
        
        // Draw ground check
        Gizmos.color = Color.yellow;
        Gizmos.DrawLine(transform.position, transform.position + Vector3.down * groundCheckDistance);
        
        // Draw max altitude
        Gizmos.color = Color.red;
        Gizmos.DrawWireCube(transform.position + Vector3.up * maxAltitude, Vector3.one);
    }
}
