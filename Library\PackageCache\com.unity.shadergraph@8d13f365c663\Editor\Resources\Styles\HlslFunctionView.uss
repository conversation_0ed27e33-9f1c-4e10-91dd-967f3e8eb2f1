HlslFunctionView {
    margin-top: 2;
    margin-bottom: 2;
    margin-left: 4;
    margin-right: 4;
}

HlslFunctionView > #Row {
    flex-direction: row;
    flex-grow: 1;
    margin-top: 2;
    margin-bottom: 2;
    margin-left: 4;
    margin-right: 4;
}

.unity-label {
    width: 80;
    margin-top: 0;
    margin-bottom: 0;
}

.unity-base-field {
    flex-direction: column;
    flex-grow: 1;
    margin-top: 0;
    margin-bottom: 0;
}

.unity-base-text-field__input {
    flex-direction: column;
    flex-grow: 1;
    -unity-text-align: upper-left;
    overflow: hidden;
}

.sg-hlsl-function-view__body {
    align-self: stretch;
    flex-shrink: 1;
}
