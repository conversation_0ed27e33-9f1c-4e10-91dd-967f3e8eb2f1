Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.11f1 (9b156bbbd4df) revision 10163563'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 32628 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-07-14T17:14:23Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.11f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Users/<USER>/OneDrive/Desktop/space game
-logFile
Logs/AssetImportWorker1.log
-srvPort
21488
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/OneDrive/Desktop/space game
C:/Users/<USER>/OneDrive/Desktop/space game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [28552]  Target information:

Player connection [28552]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2995007622 [EditorId] 2995007622 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [28552]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 2995007622 [EditorId] 2995007622 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [28552]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 2995007622 [EditorId] 2995007622 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [28552]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2995007622 [EditorId] 2995007622 [Version] 1048832 [Id] WindowsEditor(7,Jakes) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [28552] Host joined multi-casting on [***********:54997]...
Player connection [28552] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.11f1 (9b156bbbd4df)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/OneDrive/Desktop/space game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 2060 SUPER (ID=0x1f06)
    Vendor:          NVIDIA
    VRAM:            8006 MB
    App VRAM Budget: 7238 MB
    Driver:          32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56980
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.11f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004384 seconds.
- Loaded All Assemblies, in  0.607 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.593 seconds
Domain Reload Profiling: 1195ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (76ms)
	LoadAllAssembliesAndSetupDomain (256ms)
		LoadAssemblies (192ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (252ms)
			TypeCache.Refresh (249ms)
				TypeCache.ScanAssembly (230ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (594ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (514ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (83ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (214ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.356 seconds
Refreshing native plugins compatible for Editor in 2.02 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.263 seconds
Domain Reload Profiling: 2609ms
	BeginReloadAssembly (269ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (74ms)
	LoadAllAssembliesAndSetupDomain (922ms)
		LoadAssemblies (654ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (405ms)
			TypeCache.Refresh (312ms)
				TypeCache.ScanAssembly (283ms)
			BuildScriptInfoCaches (73ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1264ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1068ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (242ms)
			ProcessInitializeOnLoadAttributes (734ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (14ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 2.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 132 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5990 unused Assets / (7.2 MB). Loaded Objects now: 6535.
Memory consumption went from 149.2 MB to 142.1 MB.
Total: 16.670600 ms (FindLiveObjects: 1.050500 ms CreateObjectMapping: 1.670700 ms MarkObjects: 6.345200 ms  DeleteObjects: 7.602700 ms)

========================================================================
Received Import Request.
  Time since last request: 7113.828513 seconds.
  path: Assets/DefaultVolumeProfile.asset
  artifactKey: Guid(0079f8f52c999654ba28ac2405d82ca3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/DefaultVolumeProfile.asset using Guid(0079f8f52c999654ba28ac2405d82ca3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5646450a31c12165b47f8f9865696131') in 0.0288365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.379 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.538 seconds
Domain Reload Profiling: 2910ms
	BeginReloadAssembly (329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (898ms)
		LoadAssemblies (639ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (420ms)
			TypeCache.Refresh (221ms)
				TypeCache.ScanAssembly (203ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1539ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1357ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (217ms)
			ProcessInitializeOnLoadAttributes (965ms)
			ProcessInitializeOnLoadMethodAttributes (152ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 4.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5988 unused Assets / (6.0 MB). Loaded Objects now: 6540.
Memory consumption went from 133.1 MB to 127.1 MB.
Total: 14.926600 ms (FindLiveObjects: 0.838600 ms CreateObjectMapping: 1.249400 ms MarkObjects: 8.072100 ms  DeleteObjects: 4.764900 ms)

Prepare: number of updated asset objects reloaded= 0
