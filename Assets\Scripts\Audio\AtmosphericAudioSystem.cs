using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Creates eerie atmospheric audio for exploring derelict ships and space stations
/// </summary>
public class AtmosphericAudioSystem : MonoBehaviour
{
    [Header("Atmospheric Settings")]
    [SerializeField] private List<AtmosphericZone> atmosphericZones = new List<AtmosphericZone>();
    [SerializeField] private float fadeSpeed = 2f;
    [SerializeField] private bool enableRandomEvents = true;
    [SerializeField] private float randomEventInterval = 30f;
    
    [Header("Audio Sources")]
    [SerializeField] private AudioSource ambientSource;
    [SerializeField] private AudioSource eerieSource;
    [SerializeField] private AudioSource randomEventSource;
    
    [Header("Eerie Sounds")]
    [SerializeField] private List<AudioClip> eerieAmbientSounds = new List<AudioClip>();
    [SerializeField] private List<AudioClip> randomEerieSounds = new List<AudioClip>();
    [SerializeField] private List<AudioClip> shipCreakingSounds = new List<AudioClip>();
    [SerializeField] private List<AudioClip> distantMachineSounds = new List<AudioClip>();
    [SerializeField] private List<AudioClip> whisperSounds = new List<AudioClip>();
    
    public static AtmosphericAudioSystem Instance { get; private set; }
    
    private AtmosphericZone currentZone;
    private Coroutine fadeCoroutine;
    private Coroutine randomEventCoroutine;
    private Transform player;
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        InitializeAudioSystem();
        
        if (enableRandomEvents)
        {
            randomEventCoroutine = StartCoroutine(RandomEventRoutine());
        }
    }
    
    private void InitializeAudioSystem()
    {
        // Find player
        GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
        if (playerObj != null)
            player = playerObj.transform;
        
        // Create audio sources if not assigned
        if (ambientSource == null)
        {
            GameObject ambientObj = new GameObject("AmbientAudioSource");
            ambientObj.transform.SetParent(transform);
            ambientSource = ambientObj.AddComponent<AudioSource>();
            ambientSource.loop = true;
            ambientSource.spatialBlend = 0f; // 2D
        }
        
        if (eerieSource == null)
        {
            GameObject eerieObj = new GameObject("EerieAudioSource");
            eerieObj.transform.SetParent(transform);
            eerieSource = eerieObj.AddComponent<AudioSource>();
            eerieSource.loop = true;
            eerieSource.spatialBlend = 0f; // 2D
        }
        
        if (randomEventSource == null)
        {
            GameObject eventObj = new GameObject("RandomEventAudioSource");
            eventObj.transform.SetParent(transform);
            randomEventSource = eventObj.AddComponent<AudioSource>();
            randomEventSource.spatialBlend = 1f; // 3D
        }
        
        // Create default atmospheric zones
        CreateDefaultAtmosphericZones();
    }
    
    private void CreateDefaultAtmosphericZones()
    {
        if (atmosphericZones.Count > 0) return;
        
        // Safe Zone (Your ship/station)
        atmosphericZones.Add(new AtmosphericZone
        {
            zoneName = "Safe Zone",
            zoneType = AtmosphereType.Safe,
            ambientVolume = 0.3f,
            eerieVolume = 0f,
            enableRandomEvents = false,
            randomEventChance = 0f
        });
        
        // Derelict Ship - Light Atmosphere
        atmosphericZones.Add(new AtmosphericZone
        {
            zoneName = "Derelict Ship - Outer",
            zoneType = AtmosphereType.DerelictLight,
            ambientVolume = 0.4f,
            eerieVolume = 0.2f,
            enableRandomEvents = true,
            randomEventChance = 0.3f
        });
        
        // Derelict Ship - Heavy Atmosphere
        atmosphericZones.Add(new AtmosphericZone
        {
            zoneName = "Derelict Ship - Inner",
            zoneType = AtmosphereType.DerelictHeavy,
            ambientVolume = 0.2f,
            eerieVolume = 0.6f,
            enableRandomEvents = true,
            randomEventChance = 0.7f
        });
        
        // Abandoned Station
        atmosphericZones.Add(new AtmosphericZone
        {
            zoneName = "Abandoned Station",
            zoneType = AtmosphereType.AbandonedStation,
            ambientVolume = 0.3f,
            eerieVolume = 0.5f,
            enableRandomEvents = true,
            randomEventChance = 0.5f
        });
        
        // Deep Space
        atmosphericZones.Add(new AtmosphericZone
        {
            zoneName = "Deep Space",
            zoneType = AtmosphereType.DeepSpace,
            ambientVolume = 0.1f,
            eerieVolume = 0.3f,
            enableRandomEvents = true,
            randomEventChance = 0.2f
        });
    }
    
    public void SetAtmosphericZone(AtmosphereType atmosphereType)
    {
        AtmosphericZone zone = atmosphericZones.Find(z => z.zoneType == atmosphereType);
        if (zone != null)
        {
            SetAtmosphericZone(zone);
        }
    }
    
    public void SetAtmosphericZone(string zoneName)
    {
        AtmosphericZone zone = atmosphericZones.Find(z => z.zoneName == zoneName);
        if (zone != null)
        {
            SetAtmosphericZone(zone);
        }
    }
    
    public void SetAtmosphericZone(AtmosphericZone zone)
    {
        if (zone == currentZone) return;
        
        currentZone = zone;
        
        if (fadeCoroutine != null)
            StopCoroutine(fadeCoroutine);
            
        fadeCoroutine = StartCoroutine(FadeToAtmosphere(zone));
        
        Debug.Log($"Switched to atmospheric zone: {zone.zoneName}");
    }
    
    private IEnumerator FadeToAtmosphere(AtmosphericZone zone)
    {
        // Fade out current audio
        float startAmbientVolume = ambientSource.volume;
        float startEerieVolume = eerieSource.volume;
        
        while (ambientSource.volume > 0f || eerieSource.volume > 0f)
        {
            ambientSource.volume = Mathf.MoveTowards(ambientSource.volume, 0f, fadeSpeed * Time.deltaTime);
            eerieSource.volume = Mathf.MoveTowards(eerieSource.volume, 0f, fadeSpeed * Time.deltaTime);
            yield return null;
        }
        
        // Change audio clips based on zone type
        SetAudioClipsForZone(zone);
        
        // Fade in new audio
        while (ambientSource.volume < zone.ambientVolume || eerieSource.volume < zone.eerieVolume)
        {
            ambientSource.volume = Mathf.MoveTowards(ambientSource.volume, zone.ambientVolume, fadeSpeed * Time.deltaTime);
            eerieSource.volume = Mathf.MoveTowards(eerieSource.volume, zone.eerieVolume, fadeSpeed * Time.deltaTime);
            yield return null;
        }
    }
    
    private void SetAudioClipsForZone(AtmosphericZone zone)
    {
        // Set ambient audio based on zone type
        switch (zone.zoneType)
        {
            case AtmosphereType.Safe:
                SetAmbientClip(null); // No eerie sounds in safe zones
                SetEerieClip(null);
                break;
                
            case AtmosphereType.DerelictLight:
                SetRandomAmbientClip();
                SetRandomEerieClip();
                break;
                
            case AtmosphereType.DerelictHeavy:
                SetRandomAmbientClip();
                SetRandomEerieClip();
                break;
                
            case AtmosphereType.AbandonedStation:
                SetRandomAmbientClip();
                SetRandomEerieClip();
                break;
                
            case AtmosphereType.DeepSpace:
                SetAmbientClip(null); // Silent space
                SetRandomEerieClip();
                break;
        }
    }
    
    private void SetAmbientClip(AudioClip clip)
    {
        if (clip != null)
        {
            ambientSource.clip = clip;
            if (!ambientSource.isPlaying)
                ambientSource.Play();
        }
        else
        {
            ambientSource.Stop();
        }
    }
    
    private void SetEerieClip(AudioClip clip)
    {
        if (clip != null)
        {
            eerieSource.clip = clip;
            if (!eerieSource.isPlaying)
                eerieSource.Play();
        }
        else
        {
            eerieSource.Stop();
        }
    }
    
    private void SetRandomAmbientClip()
    {
        if (eerieAmbientSounds.Count > 0)
        {
            AudioClip clip = eerieAmbientSounds[Random.Range(0, eerieAmbientSounds.Count)];
            SetAmbientClip(clip);
        }
    }
    
    private void SetRandomEerieClip()
    {
        if (eerieAmbientSounds.Count > 0)
        {
            AudioClip clip = eerieAmbientSounds[Random.Range(0, eerieAmbientSounds.Count)];
            SetEerieClip(clip);
        }
    }
    
    private IEnumerator RandomEventRoutine()
    {
        while (true)
        {
            yield return new WaitForSeconds(randomEventInterval + Random.Range(-10f, 10f));
            
            if (currentZone != null && currentZone.enableRandomEvents)
            {
                if (Random.value <= currentZone.randomEventChance)
                {
                    PlayRandomEvent();
                }
            }
        }
    }
    
    private void PlayRandomEvent()
    {
        List<AudioClip> eventSounds = new List<AudioClip>();
        
        // Add different types of random sounds based on current zone
        if (currentZone.zoneType == AtmosphereType.DerelictLight || currentZone.zoneType == AtmosphereType.DerelictHeavy)
        {
            eventSounds.AddRange(shipCreakingSounds);
            eventSounds.AddRange(distantMachineSounds);
        }
        
        if (currentZone.zoneType == AtmosphereType.DerelictHeavy || currentZone.zoneType == AtmosphereType.AbandonedStation)
        {
            eventSounds.AddRange(whisperSounds);
        }
        
        eventSounds.AddRange(randomEerieSounds);
        
        if (eventSounds.Count > 0)
        {
            AudioClip eventClip = eventSounds[Random.Range(0, eventSounds.Count)];
            
            // Play at random position around player
            if (player != null)
            {
                Vector3 randomPos = player.position + Random.insideUnitSphere * 20f;
                randomPos.y = player.position.y; // Keep at player height
                
                randomEventSource.transform.position = randomPos;
                randomEventSource.PlayOneShot(eventClip);
                
                Debug.Log($"Played random atmospheric event: {eventClip.name}");
            }
        }
    }
    
    public void PlayCustomEvent(AudioClip clip, Vector3 position)
    {
        if (clip != null)
        {
            randomEventSource.transform.position = position;
            randomEventSource.PlayOneShot(clip);
        }
    }
    
    public void SetMasterVolume(float volume)
    {
        if (currentZone != null)
        {
            ambientSource.volume = currentZone.ambientVolume * volume;
            eerieSource.volume = currentZone.eerieVolume * volume;
        }
    }
    
    // Properties
    public AtmosphericZone CurrentZone => currentZone;
    public bool IsInSafeZone => currentZone != null && currentZone.zoneType == AtmosphereType.Safe;
    public bool IsInDangerousZone => currentZone != null && 
        (currentZone.zoneType == AtmosphereType.DerelictHeavy || currentZone.zoneType == AtmosphereType.AbandonedStation);
}

[System.Serializable]
public class AtmosphericZone
{
    public string zoneName;
    public AtmosphereType zoneType;
    [Range(0f, 1f)] public float ambientVolume = 0.5f;
    [Range(0f, 1f)] public float eerieVolume = 0.3f;
    public bool enableRandomEvents = true;
    [Range(0f, 1f)] public float randomEventChance = 0.5f;
}

public enum AtmosphereType
{
    Safe,
    DerelictLight,
    DerelictHeavy,
    AbandonedStation,
    DeepSpace,
    EngineRoom,
    MedicalBay,
    Bridge
}
