using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Manages player equipment and gear
/// </summary>
public class PlayerEquipment : MonoBehaviour
{
    [Header("Equipment Slots")]
    [SerializeField] private Transform helmetSlot;
    [SerializeField] private Transform suitSlot;
    [SerializeField] private Transform glovesSlot;
    [SerializeField] private Transform bootsSlot;
    [SerializeField] private Transform backpackSlot;
    [SerializeField] private Transform primaryWeaponSlot;
    [SerializeField] private Transform secondaryWeaponSlot;
    [SerializeField] private Transform toolSlot;
    
    [Header("Audio")]
    [SerializeField] private AudioClip equipSound;
    [SerializeField] private AudioClip unequipSound;
    
    // Equipment storage
    private Dictionary<EquipmentSlot, Item> equippedItems = new Dictionary<EquipmentSlot, Item>();
    private Dictionary<EquipmentSlot, GameObject> equippedModels = new Dictionary<EquipmentSlot, GameObject>();
    
    // Components
    private PlayerResources playerResources;
    private PlayerController playerController;
    private AudioSource audioSource;
    
    // Properties
    public float TotalProtection { get; private set; }
    public float TotalOxygenCapacity { get; private set; }
    public float MovementSpeedModifier { get; private set; }
    
    // Events
    public System.Action<EquipmentSlot, Item> OnItemEquipped;
    public System.Action<EquipmentSlot, Item> OnItemUnequipped;
    
    private void Start()
    {
        InitializeEquipment();
    }
    
    private void InitializeEquipment()
    {
        playerResources = GetComponent<PlayerResources>();
        playerController = GetComponent<PlayerController>();
        audioSource = GetComponent<AudioSource>();
        
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
            
        // Initialize equipment slots
        foreach (EquipmentSlot slot in System.Enum.GetValues(typeof(EquipmentSlot)))
        {
            if (slot != EquipmentSlot.None)
            {
                equippedItems[slot] = null;
                equippedModels[slot] = null;
            }
        }
        
        UpdateEquipmentStats();
    }
    
    /// <summary>
    /// Equip an item to the appropriate slot
    /// </summary>
    public bool EquipItem(Item item)
    {
        if (item == null || item.equipmentSlot == EquipmentSlot.None)
            return false;
            
        EquipmentSlot slot = item.equipmentSlot;
        
        // Unequip current item in slot if any
        if (equippedItems[slot] != null)
        {
            UnequipItem(slot);
        }
        
        // Equip new item
        equippedItems[slot] = item;
        
        // Create visual model
        CreateEquipmentModel(slot, item);
        
        // Update stats
        UpdateEquipmentStats();
        
        // Play sound
        PlayEquipSound(item.equipSound ?? equipSound);
        
        // Trigger event
        OnItemEquipped?.Invoke(slot, item);
        
        Debug.Log($"Equipped {item.itemName} to {slot}");
        return true;
    }
    
    /// <summary>
    /// Unequip an item from a slot
    /// </summary>
    public bool UnequipItem(EquipmentSlot slot)
    {
        if (!equippedItems.ContainsKey(slot) || equippedItems[slot] == null)
            return false;
            
        Item item = equippedItems[slot];
        
        // Remove from slot
        equippedItems[slot] = null;
        
        // Destroy visual model
        DestroyEquipmentModel(slot);
        
        // Update stats
        UpdateEquipmentStats();
        
        // Play sound
        PlayEquipSound(item.unequipSound ?? unequipSound);
        
        // Try to add back to inventory
        PlayerInventory inventory = GetComponent<PlayerInventory>();
        if (inventory != null)
        {
            if (!inventory.AddItem(item))
            {
                // If inventory is full, drop the item
                inventory.DropItem(item);
            }
        }
        
        // Trigger event
        OnItemUnequipped?.Invoke(slot, item);
        
        Debug.Log($"Unequipped {item.itemName} from {slot}");
        return true;
    }
    
    /// <summary>
    /// Get equipped item in a specific slot
    /// </summary>
    public Item GetEquippedItem(EquipmentSlot slot)
    {
        return equippedItems.ContainsKey(slot) ? equippedItems[slot] : null;
    }
    
    /// <summary>
    /// Check if a slot has an item equipped
    /// </summary>
    public bool IsSlotEquipped(EquipmentSlot slot)
    {
        return GetEquippedItem(slot) != null;
    }
    
    /// <summary>
    /// Get all equipped items
    /// </summary>
    public Dictionary<EquipmentSlot, Item> GetAllEquippedItems()
    {
        return new Dictionary<EquipmentSlot, Item>(equippedItems);
    }
    
    private void CreateEquipmentModel(EquipmentSlot slot, Item item)
    {
        Transform parentSlot = GetSlotTransform(slot);
        if (parentSlot == null || item.worldModel == null) return;
        
        // Destroy existing model
        DestroyEquipmentModel(slot);
        
        // Create new model
        GameObject model = Instantiate(item.worldModel, parentSlot);
        model.name = $"{item.itemName}_Model";
        
        // Remove any colliders from the model (it's just visual)
        Collider[] colliders = model.GetComponentsInChildren<Collider>();
        foreach (Collider col in colliders)
        {
            col.enabled = false;
        }
        
        equippedModels[slot] = model;
    }
    
    private void DestroyEquipmentModel(EquipmentSlot slot)
    {
        if (equippedModels.ContainsKey(slot) && equippedModels[slot] != null)
        {
            Destroy(equippedModels[slot]);
            equippedModels[slot] = null;
        }
    }
    
    private Transform GetSlotTransform(EquipmentSlot slot)
    {
        switch (slot)
        {
            case EquipmentSlot.Helmet: return helmetSlot;
            case EquipmentSlot.Suit: return suitSlot;
            case EquipmentSlot.Gloves: return glovesSlot;
            case EquipmentSlot.Boots: return bootsSlot;
            case EquipmentSlot.Backpack: return backpackSlot;
            case EquipmentSlot.PrimaryWeapon: return primaryWeaponSlot;
            case EquipmentSlot.SecondaryWeapon: return secondaryWeaponSlot;
            case EquipmentSlot.Tool: return toolSlot;
            default: return null;
        }
    }
    
    private void UpdateEquipmentStats()
    {
        TotalProtection = 0f;
        TotalOxygenCapacity = 0f;
        MovementSpeedModifier = 0f;
        
        foreach (Item item in equippedItems.Values)
        {
            if (item != null)
            {
                TotalProtection += item.protection;
                TotalOxygenCapacity += item.oxygenCapacity;
                MovementSpeedModifier += item.movementSpeedModifier;
            }
        }
        
        // Apply movement speed modifier to player controller
        if (playerController != null)
        {
            // This would need to be implemented in PlayerController
            // playerController.SetSpeedModifier(MovementSpeedModifier);
        }
    }
    
    private void PlayEquipSound(AudioClip clip)
    {
        if (clip != null && audioSource != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    /// <summary>
    /// Calculate damage reduction based on equipped protection
    /// </summary>
    public float CalculateDamageReduction(float incomingDamage)
    {
        float damageReduction = TotalProtection / 100f; // Convert to percentage
        damageReduction = Mathf.Clamp01(damageReduction); // Cap at 100%
        return incomingDamage * (1f - damageReduction);
    }
    
    /// <summary>
    /// Get equipment save data
    /// </summary>
    public EquipmentSaveData GetSaveData()
    {
        EquipmentSaveData saveData = new EquipmentSaveData();
        saveData.equippedItems = new Dictionary<string, string>();
        
        foreach (var kvp in equippedItems)
        {
            if (kvp.Value != null)
            {
                saveData.equippedItems[kvp.Key.ToString()] = kvp.Value.name;
            }
        }
        
        return saveData;
    }
    
    /// <summary>
    /// Load equipment save data
    /// </summary>
    public void LoadSaveData(EquipmentSaveData saveData)
    {
        // Unequip all current items
        foreach (EquipmentSlot slot in System.Enum.GetValues(typeof(EquipmentSlot)))
        {
            if (slot != EquipmentSlot.None && IsSlotEquipped(slot))
            {
                UnequipItem(slot);
            }
        }
        
        // Equip saved items
        foreach (var kvp in saveData.equippedItems)
        {
            if (System.Enum.TryParse(kvp.Key, out EquipmentSlot slot))
            {
                Item item = Resources.Load<Item>($"Items/{kvp.Value}");
                if (item != null)
                {
                    EquipItem(item);
                }
            }
        }
    }
}

[System.Serializable]
public class EquipmentSaveData
{
    public Dictionary<string, string> equippedItems = new Dictionary<string, string>();
}
