using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Manages missions and objectives in the space game
/// </summary>
public class MissionSystem : MonoBehaviour
{
    [Header("Mission Settings")]
    [SerializeField] private List<Mission> availableMissions = new List<Mission>();
    [SerializeField] private bool debugMode = false;
    
    public static MissionSystem Instance { get; private set; }
    
    private List<Mission> activeMissions = new List<Mission>();
    private List<Mission> completedMissions = new List<Mission>();
    
    // Events
    public System.Action<Mission> OnMissionStarted;
    public System.Action<Mission> OnMissionCompleted;
    public System.Action<Mission, Objective> OnObjectiveCompleted;
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        SubscribeToEvents();
    }
    
    private void SubscribeToEvents()
    {
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Subscribe<ItemPickedUpEvent>(OnItemPickedUp);
            EventManager.Instance.Subscribe<DoorOpenedEvent>(OnDoorOpened);
            EventManager.Instance.Subscribe<VehicleEnteredEvent>(OnVehicleEntered);
            EventManager.Instance.Subscribe<EnemyDefeatedEvent>(OnEnemyDefeated);
        }
    }
    
    public void StartMission(string missionId)
    {
        Mission mission = availableMissions.Find(m => m.missionId == missionId);
        if (mission == null)
        {
            Debug.LogWarning($"Mission '{missionId}' not found!");
            return;
        }
        
        if (activeMissions.Contains(mission))
        {
            Debug.LogWarning($"Mission '{missionId}' is already active!");
            return;
        }
        
        // Check prerequisites
        if (!CheckMissionPrerequisites(mission))
        {
            Debug.LogWarning($"Prerequisites not met for mission '{missionId}'!");
            return;
        }
        
        // Start mission
        activeMissions.Add(mission);
        mission.StartMission();
        
        OnMissionStarted?.Invoke(mission);
        
        if (AudioManager.Instance != null)
            AudioManager.Instance.PlaySFX("mission");
            
        Debug.Log($"Started mission: {mission.missionName}");
    }
    
    public void CompleteMission(string missionId)
    {
        Mission mission = activeMissions.Find(m => m.missionId == missionId);
        if (mission == null) return;
        
        activeMissions.Remove(mission);
        completedMissions.Add(mission);
        
        mission.CompleteMission();
        
        OnMissionCompleted?.Invoke(mission);
        
        // Trigger mission completed event
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new MissionCompletedEvent
            {
                missionId = mission.missionId,
                missionName = mission.missionName,
                experienceGained = mission.experienceReward
            });
        }
        
        if (AudioManager.Instance != null)
            AudioManager.Instance.PlaySFX("achievement");
            
        Debug.Log($"Completed mission: {mission.missionName}");
    }
    
    public void CompleteObjective(string missionId, string objectiveId)
    {
        Mission mission = activeMissions.Find(m => m.missionId == missionId);
        if (mission == null) return;
        
        Objective objective = mission.objectives.Find(o => o.objectiveId == objectiveId);
        if (objective == null || objective.isCompleted) return;
        
        objective.CompleteObjective();
        OnObjectiveCompleted?.Invoke(mission, objective);
        
        // Trigger objective completed event
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new ObjectiveCompletedEvent
            {
                objectiveId = objective.objectiveId,
                missionId = mission.missionId,
                description = objective.description
            });
        }
        
        // Check if mission is complete
        if (mission.IsCompleted)
        {
            CompleteMission(missionId);
        }
        
        if (debugMode)
            Debug.Log($"Completed objective: {objective.description}");
    }
    
    public void UpdateObjectiveProgress(string missionId, string objectiveId, int progress)
    {
        Mission mission = activeMissions.Find(m => m.missionId == missionId);
        if (mission == null) return;
        
        Objective objective = mission.objectives.Find(o => o.objectiveId == objectiveId);
        if (objective == null || objective.isCompleted) return;
        
        objective.currentProgress = Mathf.Min(objective.targetProgress, objective.currentProgress + progress);
        
        if (objective.currentProgress >= objective.targetProgress)
        {
            CompleteObjective(missionId, objectiveId);
        }
    }
    
    private bool CheckMissionPrerequisites(Mission mission)
    {
        foreach (string prerequisiteId in mission.prerequisiteMissions)
        {
            if (!completedMissions.Any(m => m.missionId == prerequisiteId))
            {
                return false;
            }
        }
        return true;
    }
    
    // Event handlers for automatic objective completion
    private void OnItemPickedUp(ItemPickedUpEvent itemEvent)
    {
        foreach (Mission mission in activeMissions)
        {
            foreach (Objective objective in mission.objectives)
            {
                if (objective.objectiveType == ObjectiveType.CollectItem && 
                    objective.targetItem == itemEvent.itemName && 
                    !objective.isCompleted)
                {
                    UpdateObjectiveProgress(mission.missionId, objective.objectiveId, itemEvent.quantity);
                }
            }
        }
    }
    
    private void OnDoorOpened(DoorOpenedEvent doorEvent)
    {
        foreach (Mission mission in activeMissions)
        {
            foreach (Objective objective in mission.objectives)
            {
                if (objective.objectiveType == ObjectiveType.OpenDoor && 
                    objective.targetObject == doorEvent.door.name && 
                    !objective.isCompleted)
                {
                    CompleteObjective(mission.missionId, objective.objectiveId);
                }
            }
        }
    }
    
    private void OnVehicleEntered(VehicleEnteredEvent vehicleEvent)
    {
        foreach (Mission mission in activeMissions)
        {
            foreach (Objective objective in mission.objectives)
            {
                if (objective.objectiveType == ObjectiveType.EnterVehicle && 
                    objective.targetObject == vehicleEvent.vehicle.name && 
                    !objective.isCompleted)
                {
                    CompleteObjective(mission.missionId, objective.objectiveId);
                }
            }
        }
    }
    
    private void OnEnemyDefeated(EnemyDefeatedEvent enemyEvent)
    {
        foreach (Mission mission in activeMissions)
        {
            foreach (Objective objective in mission.objectives)
            {
                if (objective.objectiveType == ObjectiveType.DefeatEnemies && 
                    (string.IsNullOrEmpty(objective.targetObject) || objective.targetObject == enemyEvent.enemyType) && 
                    !objective.isCompleted)
                {
                    UpdateObjectiveProgress(mission.missionId, objective.objectiveId, 1);
                }
            }
        }
    }
    
    // Public getters
    public List<Mission> GetActiveMissions() => new List<Mission>(activeMissions);
    public List<Mission> GetCompletedMissions() => new List<Mission>(completedMissions);
    public List<Mission> GetAvailableMissions() => availableMissions.Where(m => 
        !activeMissions.Contains(m) && 
        !completedMissions.Contains(m) && 
        CheckMissionPrerequisites(m)).ToList();
    
    public Mission GetMission(string missionId)
    {
        return availableMissions.Find(m => m.missionId == missionId);
    }
    
    public bool IsMissionActive(string missionId)
    {
        return activeMissions.Any(m => m.missionId == missionId);
    }
    
    public bool IsMissionCompleted(string missionId)
    {
        return completedMissions.Any(m => m.missionId == missionId);
    }
    
    private void OnDestroy()
    {
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Unsubscribe<ItemPickedUpEvent>(OnItemPickedUp);
            EventManager.Instance.Unsubscribe<DoorOpenedEvent>(OnDoorOpened);
            EventManager.Instance.Unsubscribe<VehicleEnteredEvent>(OnVehicleEntered);
            EventManager.Instance.Unsubscribe<EnemyDefeatedEvent>(OnEnemyDefeated);
        }
    }
}

/// <summary>
/// Mission data structure
/// </summary>
[System.Serializable]
public class Mission
{
    [Header("Mission Info")]
    public string missionId;
    public string missionName;
    [TextArea(3, 5)]
    public string description;
    public MissionType missionType;
    
    [Header("Requirements")]
    public List<string> prerequisiteMissions = new List<string>();
    public int requiredLevel = 1;
    
    [Header("Objectives")]
    public List<Objective> objectives = new List<Objective>();
    
    [Header("Rewards")]
    public int experienceReward = 100;
    public int creditReward = 50;
    public List<string> itemRewards = new List<string>();
    
    [Header("Status")]
    public bool isStarted = false;
    public bool isCompleted = false;
    
    public bool IsCompleted => objectives.All(o => o.isCompleted);
    public float CompletionPercentage => objectives.Count > 0 ? 
        (float)objectives.Count(o => o.isCompleted) / objectives.Count : 0f;
    
    public void StartMission()
    {
        isStarted = true;
        isCompleted = false;
    }
    
    public void CompleteMission()
    {
        isCompleted = true;
    }
}

/// <summary>
/// Mission objective data structure
/// </summary>
[System.Serializable]
public class Objective
{
    [Header("Objective Info")]
    public string objectiveId;
    [TextArea(2, 3)]
    public string description;
    public ObjectiveType objectiveType;
    
    [Header("Target")]
    public string targetObject;
    public string targetItem;
    public int targetProgress = 1;
    public int currentProgress = 0;
    
    [Header("Status")]
    public bool isCompleted = false;
    public bool isOptional = false;
    
    public float ProgressPercentage => targetProgress > 0 ? 
        (float)currentProgress / targetProgress : 0f;
    
    public void CompleteObjective()
    {
        isCompleted = true;
        currentProgress = targetProgress;
    }
}

public enum MissionType
{
    Main,
    Side,
    Tutorial,
    Daily,
    Emergency
}

public enum ObjectiveType
{
    GoToLocation,
    CollectItem,
    DefeatEnemies,
    TalkToNPC,
    OpenDoor,
    ActivateDevice,
    EnterVehicle,
    SurviveTime,
    Custom
}
