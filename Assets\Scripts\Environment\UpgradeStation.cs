using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Interactive upgrade station for purchasing equipment upgrades
/// </summary>
public class UpgradeStation : InteractableBase
{
    [Header("Upgrade Station Settings")]
    [SerializeField] private bool isPowered = true;
    [SerializeField] private UpgradeStationType stationType = UpgradeStationType.Universal;
    [SerializeField] private List<UpgradeType> allowedUpgradeTypes = new List<UpgradeType>();
    
    [Header("Visual Effects")]
    [SerializeField] private Light stationLight;
    [SerializeField] private Color poweredColor = Color.green;
    [SerializeField] private Color unpoweredColor = Color.red;
    [SerializeField] private Renderer screenRenderer;
    [SerializeField] private Material poweredScreenMaterial;
    [SerializeField] private Material unpoweredScreenMaterial;
    [SerializeField] private ParticleSystem upgradeParticles;
    
    [Header("Audio")]
    [SerializeField] private AudioClip stationBootSound;
    [SerializeField] private AudioClip upgradeSuccessSound;
    [SerializeField] private AudioClip upgradeFailSound;
    [SerializeField] private AudioClip stationHumSound;
    
    private UpgradeStationUI stationUI;
    private AudioSource audioSource;
    private bool isInUse = false;
    
    public enum UpgradeStationType
    {
        Universal,      // Can upgrade everything
        Equipment,      // Flashlight, weapons, suit
        Spacecraft,     // Ship upgrades only
        Weapons,        // Weapons only
        LifeSupport     // Suit and life support only
    }
    
    protected override void Awake()
    {
        base.Awake();
        
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
        
        stationUI = FindObjectOfType<UpgradeStationUI>();
        
        // Set allowed upgrade types based on station type
        SetAllowedUpgradeTypes();
        
        UpdateVisuals();
        UpdateInteractionText();
    }
    
    private void Start()
    {
        // Play station hum sound
        if (isPowered && stationHumSound != null)
        {
            audioSource.clip = stationHumSound;
            audioSource.loop = true;
            audioSource.volume = 0.3f;
            audioSource.Play();
        }
        
        // Subscribe to power events
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Subscribe<PowerSystemChangedEvent>(OnPowerChanged);
        }
    }
    
    private void SetAllowedUpgradeTypes()
    {
        allowedUpgradeTypes.Clear();
        
        switch (stationType)
        {
            case UpgradeStationType.Universal:
                allowedUpgradeTypes.Add(UpgradeType.Flashlight);
                allowedUpgradeTypes.Add(UpgradeType.Weapon);
                allowedUpgradeTypes.Add(UpgradeType.Spacesuit);
                allowedUpgradeTypes.Add(UpgradeType.Spacecraft);
                allowedUpgradeTypes.Add(UpgradeType.Equipment);
                break;
                
            case UpgradeStationType.Equipment:
                allowedUpgradeTypes.Add(UpgradeType.Flashlight);
                allowedUpgradeTypes.Add(UpgradeType.Weapon);
                allowedUpgradeTypes.Add(UpgradeType.Spacesuit);
                allowedUpgradeTypes.Add(UpgradeType.Equipment);
                break;
                
            case UpgradeStationType.Spacecraft:
                allowedUpgradeTypes.Add(UpgradeType.Spacecraft);
                allowedUpgradeTypes.Add(UpgradeType.Ship);
                break;
                
            case UpgradeStationType.Weapons:
                allowedUpgradeTypes.Add(UpgradeType.Weapon);
                break;
                
            case UpgradeStationType.LifeSupport:
                allowedUpgradeTypes.Add(UpgradeType.Spacesuit);
                break;
        }
    }
    
    public override void Interact(GameObject player)
    {
        if (!isPowered)
        {
            PlaySound(upgradeFailSound);
            Debug.Log("Upgrade station has no power!");
            return;
        }
        
        if (isInUse)
        {
            CloseUpgradeStation();
        }
        else
        {
            OpenUpgradeStation(player);
        }
    }
    
    public override void OnInteractionEnter(GameObject player)
    {
        base.OnInteractionEnter(player);
        UpdateInteractionText();
    }
    
    private void OpenUpgradeStation(GameObject player)
    {
        isInUse = true;
        
        // Play boot sound
        PlaySound(stationBootSound);
        
        // Change game state
        if (GameManager.Instance != null)
        {
            GameManager.Instance.SetState(GameState.Inventory); // Reuse inventory state for upgrade UI
        }
        
        // Show upgrade UI
        if (stationUI != null)
        {
            stationUI.ShowUpgradeStation(this, allowedUpgradeTypes);
        }
        else
        {
            // Fallback: Show available upgrades in console
            ShowUpgradesInConsole();
        }
        
        UpdateInteractionText();
        Debug.Log($"Opened {stationType} upgrade station");
    }
    
    private void CloseUpgradeStation()
    {
        isInUse = false;
        
        // Hide upgrade UI
        if (stationUI != null)
        {
            stationUI.HideUpgradeStation();
        }
        
        // Return to game
        if (GameManager.Instance != null)
        {
            GameManager.Instance.SetState(GameState.Playing);
        }
        
        UpdateInteractionText();
        Debug.Log("Closed upgrade station");
    }
    
    private void ShowUpgradesInConsole()
    {
        if (UpgradeSystem.Instance == null)
        {
            Debug.Log("Upgrade system not available");
            return;
        }
        
        Debug.Log($"=== {stationType.ToString().ToUpper()} UPGRADE STATION ===");
        Debug.Log($"Credits: {UpgradeSystem.Instance.Credits} | Scrap: {UpgradeSystem.Instance.Scrap}");
        Debug.Log("");
        
        var categories = UpgradeSystem.Instance.GetAllCategories();
        
        foreach (var category in categories)
        {
            bool hasAllowedUpgrades = false;
            
            foreach (var upgrade in category.upgrades)
            {
                if (allowedUpgradeTypes.Contains(upgrade.upgradeType))
                {
                    if (!hasAllowedUpgrades)
                    {
                        Debug.Log($"--- {category.categoryName} ---");
                        hasAllowedUpgrades = true;
                    }
                    
                    int currentLevel = UpgradeSystem.Instance.GetUpgradeLevel(upgrade.upgradeId);
                    int cost = UpgradeSystem.Instance.GetUpgradeCost(upgrade.upgradeId);
                    int scrapCost = UpgradeSystem.Instance.GetUpgradeScrapCost(upgrade.upgradeId);
                    
                    string status = currentLevel >= upgrade.maxLevel ? "[MAX]" : 
                        UpgradeSystem.Instance.CanPurchaseUpgrade(upgrade.upgradeId) ? "[AVAILABLE]" : "[INSUFFICIENT FUNDS]";
                    
                    Debug.Log($"{upgrade.upgradeName} Lv.{currentLevel}/{upgrade.maxLevel} - {cost} Credits, {scrapCost} Scrap {status}");
                    Debug.Log($"  {upgrade.description}");
                }
            }
            
            if (hasAllowedUpgrades)
                Debug.Log("");
        }
    }
    
    public bool PurchaseUpgrade(string upgradeId)
    {
        if (!isPowered || !isInUse)
            return false;
        
        if (UpgradeSystem.Instance == null)
            return false;
        
        // Check if this upgrade type is allowed at this station
        var upgrade = UpgradeSystem.Instance.GetUpgrade(upgradeId);
        if (upgrade == null || !allowedUpgradeTypes.Contains(upgrade.upgradeType))
        {
            PlaySound(upgradeFailSound);
            Debug.Log("This upgrade is not available at this station type!");
            return false;
        }
        
        bool success = UpgradeSystem.Instance.PurchaseUpgrade(upgradeId);
        
        if (success)
        {
            PlaySound(upgradeSuccessSound);
            
            // Play upgrade particles
            if (upgradeParticles != null)
                upgradeParticles.Play();
            
            Debug.Log($"Successfully purchased upgrade: {upgrade.upgradeName}");
        }
        else
        {
            PlaySound(upgradeFailSound);
            Debug.Log("Failed to purchase upgrade - insufficient funds or max level reached");
        }
        
        return success;
    }
    
    private void UpdateInteractionText()
    {
        if (!isPowered)
            interactionText = "Upgrade Station (No Power)";
        else if (isInUse)
            interactionText = "Close Upgrade Station";
        else
            interactionText = $"Use {stationType} Upgrade Station";
    }
    
    private void UpdateVisuals()
    {
        // Update station light
        if (stationLight != null)
        {
            stationLight.color = isPowered ? poweredColor : unpoweredColor;
            stationLight.enabled = isPowered;
        }
        
        // Update screen material
        if (screenRenderer != null)
        {
            if (isPowered && poweredScreenMaterial != null)
                screenRenderer.material = poweredScreenMaterial;
            else if (!isPowered && unpoweredScreenMaterial != null)
                screenRenderer.material = unpoweredScreenMaterial;
        }
    }
    
    private void PlaySound(AudioClip clip)
    {
        if (clip != null && audioSource != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    private void OnPowerChanged(PowerSystemChangedEvent powerEvent)
    {
        SetPowered(powerEvent.isPowered);
    }
    
    public void SetPowered(bool powered)
    {
        bool wasUnpowered = !isPowered;
        isPowered = powered;
        
        if (powered && wasUnpowered)
        {
            // Station powering up
            PlaySound(stationBootSound);
            
            if (stationHumSound != null)
            {
                audioSource.clip = stationHumSound;
                audioSource.loop = true;
                audioSource.volume = 0.3f;
                audioSource.Play();
            }
        }
        else if (!powered && !wasUnpowered)
        {
            // Station powering down
            audioSource.Stop();
            
            // Close station if in use
            if (isInUse)
                CloseUpgradeStation();
        }
        
        UpdateVisuals();
        UpdateInteractionText();
        canInteract = isPowered;
    }
    
    // Properties
    public bool IsPowered => isPowered;
    public bool IsInUse => isInUse;
    public UpgradeStationType StationType => stationType;
    public List<UpgradeType> AllowedUpgradeTypes => allowedUpgradeTypes;
    
    private void OnDestroy()
    {
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Unsubscribe<PowerSystemChangedEvent>(OnPowerChanged);
        }
    }
}

/// <summary>
/// Simple UI class for upgrade station interface (placeholder)
/// </summary>
public class UpgradeStationUI : MonoBehaviour
{
    public void ShowUpgradeStation(UpgradeStation station, List<UpgradeType> allowedTypes)
    {
        Debug.Log($"Showing {station.StationType} upgrade station interface");
        // This would show the actual upgrade UI
    }
    
    public void HideUpgradeStation()
    {
        Debug.Log("Hiding upgrade station interface");
    }
}
