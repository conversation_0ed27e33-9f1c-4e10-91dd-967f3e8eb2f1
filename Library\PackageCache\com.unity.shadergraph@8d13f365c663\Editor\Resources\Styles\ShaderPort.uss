/* THIS FILE IS FROM GRAPHVIEW BUT CONTAINS MODIFICATIONS */

ShaderPort {
    height: 24px;
    align-items: center;
    padding-left: 4px;
    padding-right: 4px;
    --port-color: rgb(200, 200, 200);
    --disabled-port-color: rgb(70, 70, 70);
}

ShaderPort.input {
    flex-direction: row;
}

ShaderPort.output {
    flex-direction: row-reverse;
}

ShaderPort > #connector {
    border-color: rgb(70, 70, 70);
    background-color: #212121;
    width: 8px;
    height: 8px;
    border-radius: 8px;
    align-items: center;
    justify-content: center;

    margin-left: 4px;
    margin-right: 4px;
    border-left-width: 1px;
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
}

ShaderPort > #connector:hover {
    border-color: #f0f0f0
}

ShaderPort > #connector > #cap
{
    background-color: #212121;
    width: 4px;
    height: 4px;
    border-radius: 4px;
}

ShaderPort > #connector > #cap:hover
{
    background-color: #f0f0f0;
}

ShaderPort > #connector.portHighlight {
    border-color: #f0f0f0;
}

ShaderPort > #type {
    color: #c1c1c1;
    font-size: 11px;
    height: 16px;
    padding-left: 0;
    padding-right: 0;
    margin-left: 4px;
    margin-right: 4px;
    margin-top: 4px;
}

ShaderPort.input > #type {
    -unity-text-align: middle-left;
}

ShaderPort.output > #type {
    -unity-text-align:middle-right;
}

/*******************************/
/* ShaderPorts colors by types */
/*******************************/

ShaderPort.typeTexture {
    --port-color:#FF8B8B;
}

ShaderPort.typeTexture.inactive {
    --port-color:#7F5C5C;
}

ShaderPort.typeTexture2D {
    /* Same as typeTexture */
    --port-color:#FF8B8B;
}

ShaderPort.typeTexture2D.inactive {
    /* Same as typeTexture */
    --port-color:#7F5C5C;
}

ShaderPort.typeTexture2DArray {
    /* Same as typeTexture */
    --port-color:#FF8B8B;
}

ShaderPort.typeTexture2DArray.inactive {
    /* Same as typeTexture */
    --port-color:#7F5C5C;
}

ShaderPort.typeTexture3D {
    /* Same as typeTexture */
    --port-color:#FF8B8B;
}

ShaderPort.typeTexture3D.inactive {
    /* Same as typeTexture */
    --port-color:#7F5C5C;
}

ShaderPort.typeCubemap {
    /* Same as typeTexture */
    --port-color:#FF8B8B;
}

ShaderPort.typeCubemap.inactive {
    /* Same as typeTexture */
    --port-color:#7F5C5C;
}

ShaderPort.typeGraphScript {
    /* Todo: there is no such type in Unity atm */
    --port-color:#E681BA;
}

ShaderPort.typeGraphScript.inactive {
    /* Todo: there is no such type in Unity atm */
    --port-color:#73405D;
}

ShaderPort.typeFloat4 {
    --port-color:#FBCBF4;
}

ShaderPort.typeFloat4.inactive {
    --port-color:#7D657A;
}

ShaderPort.typeVector4 {
    /* Same as typeFloat4 */
    --port-color:#FBCBF4;
}

ShaderPort.typeVector4.inactive {
    /* Same as typeFloat4 */
    --port-color:#7D657A;
}

ShaderPort.typeQuaternion {
    /* Same as typeFloat4 */
    --port-color:#FBCBF4;
}

ShaderPort.typeQuaternion.inactive {
    /* Same as typeFloat4 */
    --port-color:#7D657A;
}

ShaderPort.typeColor {
    /* Same as typeFloat4 */
    --port-color:#FBCBF4;
}

ShaderPort.typeColor.inactive {
    /* Same as typeFloat4 */
    --port-color:#7D657A;
}

ShaderPort.typeInt {
    --port-color:#9481E6;
}

ShaderPort.typeInt.inactive {
    --port-color:#4A4073;
}

ShaderPort.typeInt32 {
    /* Same as typeInt */
    --port-color:#9481E6;
}

ShaderPort.typeInt32.inactive {
    /* Same as typeInt */
    --port-color:#4A4073;
}

/* TEMP STUFF THAT SHOULD ACTUALLY STAY IN GRAPHVIEW */
ShaderPort.typeInt64 {
    /* Same as typeInt */
    /* todo we might want to differentiate that from int32 */
    --port-color:#9481E6;
}

ShaderPort.typeInt64.inactive {
    /* Same as typeInt */
    /* todo we might want to differentiate that from int32 */
    --port-color:#4A4073;
}

ShaderPort.typeBoolean {
    --port-color:#9481E6;
}

ShaderPort.typeBoolean.inactive {
    --port-color:#4A4073;
}

ShaderPort.typeMatrix {
    --port-color:#8FC1DF;
}

ShaderPort.typeMatrix.inactive {
    --port-color:#47606F;
}

ShaderPort.typeMatrix4x4 {
    /* Same as typeMatrix */
    --port-color:#8FC1DF;
}

ShaderPort.typeMatrix4x4.inactive {
    /* Same as typeMatrix */
    --port-color:#47606F;
}

ShaderPort.typeGameObject {
    --port-color:#8FC1DF;
}

ShaderPort.typeGameObject.inactive {
    --port-color:#47606F;
}

ShaderPort.typeFloat {
    --port-color:#84E4E7;
}

ShaderPort.typeFloat.inactive {
    --port-color:#427273;
}

ShaderPort.typeFloat1 {
    /* Same as typeFloat */
    --port-color:#84E4E7;
}

ShaderPort.typeFloat1.inactive {
    /* Same as typeFloat */
    --port-color:#427273;
}

ShaderPort.typeSingle {
    /* Same as typeFloat */
    --port-color:#84E4E7;
}

ShaderPort.typeSingle.inactive {
    /* Same as typeFloat */
    --port-color:#427273;
}

ShaderPort.typeDouble {
    /* Same as typeFloat */
    /* todo we might want to differentiate that from float */
    --port-color:#84E4E7;
}

ShaderPort.typeDouble.inactive {
    /* Same as typeFloat */
    /* todo we might want to differentiate that from float */
    --port-color:#427273;
}

ShaderPort.typeFloat2 {
    --port-color:#9AEF92;
}

ShaderPort.typeFloat2.inactive {
    --port-color:#4D7749;
}

ShaderPort.typeVector2 {
    /* Same as typeFloat2 */
    --port-color:#9AEF92;
}

ShaderPort.typeVector2.inactive {
    /* Same as typeFloat2 */
    --port-color:#4D7749;
}

ShaderPort.typeComponent {
    --port-color:#C9F774;
}

ShaderPort.typeComponent.inactive {
    --port-color:#647B3A;
}

ShaderPort.typeFloat3 {
    --port-color:#F6FF9A;
}

ShaderPort.typeFloat3.inactive {
    --port-color:#7B7F4D;
}

ShaderPort.typeVector3 {
    /* Same as typeFloat3 */
    --port-color:#F6FF9A;
}

ShaderPort.typeVector3.inactive {
    /* Same as typeFloat3 */
    --port-color:#7B7F4D;
}

ShaderPort.typeString {
    --port-color:#FCD76E;
}

ShaderPort.typeString.inactive {
    --port-color:#7E6B37;
}
