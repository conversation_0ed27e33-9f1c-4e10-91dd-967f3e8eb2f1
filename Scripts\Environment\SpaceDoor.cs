using UnityEngine;
using System.Collections;

/// <summary>
/// Interactable space station door that can open and close
/// </summary>
public class SpaceDoor : InteractableBase
{
    [Header("Door Settings")]
    [SerializeField] private bool isOpen = false;
    [SerializeField] private bool requiresKeycard = false;
    [SerializeField] private string requiredKeycardId = "";
    [SerializeField] private bool isPowered = true;
    [SerializeField] private float openSpeed = 2f;
    
    [Header("Door Movement")]
    [SerializeField] private Transform doorPanel;
    [SerializeField] private Vector3 openOffset = Vector3.up * 3f;
    [SerializeField] private AnimationCurve openCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    [Header("Visual Effects")]
    [SerializeField] private Light doorLight;
    [SerializeField] private Color openColor = Color.green;
    [SerializeField] private Color closedColor = Color.red;
    [SerializeField] private Color unpoweredColor = Color.gray;
    [SerializeField] private ParticleSystem doorParticles;
    
    [Header("Audio")]
    [SerializeField] private AudioClip doorOpenSound;
    [SerializeField] private AudioClip doorCloseSound;
    [SerializeField] private AudioClip doorDeniedSound;
    [SerializeField] private AudioClip doorPowerSound;
    
    private Vector3 closedPosition;
    private Vector3 openPosition;
    private Coroutine doorMovementCoroutine;
    private bool isMoving = false;
    
    protected override void Awake()
    {
        base.Awake();
        
        if (doorPanel == null)
            doorPanel = transform;
            
        closedPosition = doorPanel.localPosition;
        openPosition = closedPosition + openOffset;
        
        UpdateDoorState();
        UpdateVisuals();
    }
    
    private void Start()
    {
        // Subscribe to power system events
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Subscribe<PowerSystemChangedEvent>(OnPowerChanged);
        }
    }
    
    public override void Interact(GameObject player)
    {
        if (!isPowered)
        {
            PlayDeniedSound();
            Debug.Log("Door has no power!");
            return;
        }
        
        if (requiresKeycard && !HasRequiredKeycard(player))
        {
            PlayDeniedSound();
            Debug.Log("Access denied - keycard required!");
            return;
        }
        
        if (isMoving)
        {
            Debug.Log("Door is already moving!");
            return;
        }
        
        ToggleDoor();
    }
    
    public override void OnInteractionEnter(GameObject player)
    {
        base.OnInteractionEnter(player);
        
        if (!isPowered)
            interactionText = "Door (No Power)";
        else if (requiresKeycard && !HasRequiredKeycard(player))
            interactionText = "Door (Access Denied)";
        else
            interactionText = isOpen ? "Close Door" : "Open Door";
    }
    
    private bool HasRequiredKeycard(GameObject player)
    {
        if (!requiresKeycard) return true;
        
        PlayerInventory inventory = player.GetComponent<PlayerInventory>();
        if (inventory == null) return false;
        
        // Check for keycard item (would need to create keycard items)
        // For now, just return true if player has any items
        return inventory.Items.Count > 0;
    }
    
    private void ToggleDoor()
    {
        isOpen = !isOpen;
        
        if (doorMovementCoroutine != null)
            StopCoroutine(doorMovementCoroutine);
            
        doorMovementCoroutine = StartCoroutine(MoveDoor());
        
        // Trigger door event
        if (EventManager.Instance != null)
        {
            EventManager.Instance.TriggerEvent(new DoorOpenedEvent
            {
                door = gameObject,
                isOpen = isOpen
            });
        }
    }
    
    private IEnumerator MoveDoor()
    {
        isMoving = true;
        
        Vector3 startPosition = doorPanel.localPosition;
        Vector3 targetPosition = isOpen ? openPosition : closedPosition;
        
        // Play sound
        AudioClip soundToPlay = isOpen ? doorOpenSound : doorCloseSound;
        if (soundToPlay != null && audioSource != null)
            audioSource.PlayOneShot(soundToPlay);
            
        // Play particles
        if (doorParticles != null)
            doorParticles.Play();
        
        float elapsed = 0f;
        float duration = 1f / openSpeed;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            float curveValue = openCurve.Evaluate(t);
            
            doorPanel.localPosition = Vector3.Lerp(startPosition, targetPosition, curveValue);
            yield return null;
        }
        
        doorPanel.localPosition = targetPosition;
        isMoving = false;
        
        UpdateVisuals();
        UpdateDoorState();
    }
    
    private void UpdateDoorState()
    {
        // Update interaction text
        if (!isPowered)
            interactionText = "Door (No Power)";
        else if (requiresKeycard)
            interactionText = "Door (Keycard Required)";
        else
            interactionText = isOpen ? "Close Door" : "Open Door";
            
        // Update can interact
        canInteract = isPowered && !isMoving;
    }
    
    private void UpdateVisuals()
    {
        if (doorLight != null)
        {
            if (!isPowered)
                doorLight.color = unpoweredColor;
            else
                doorLight.color = isOpen ? openColor : closedColor;
        }
    }
    
    private void PlayDeniedSound()
    {
        if (doorDeniedSound != null && audioSource != null)
            audioSource.PlayOneShot(doorDeniedSound);
    }
    
    private void OnPowerChanged(PowerSystemChangedEvent powerEvent)
    {
        SetPowered(powerEvent.isPowered);
    }
    
    public void SetPowered(bool powered)
    {
        isPowered = powered;
        UpdateDoorState();
        UpdateVisuals();
        
        if (powered && doorPowerSound != null && audioSource != null)
            audioSource.PlayOneShot(doorPowerSound);
    }
    
    public void SetRequiresKeycard(bool requires, string keycardId = "")
    {
        requiresKeycard = requires;
        requiredKeycardId = keycardId;
        UpdateDoorState();
    }
    
    public void ForceOpen()
    {
        if (!isOpen)
            ToggleDoor();
    }
    
    public void ForceClose()
    {
        if (isOpen)
            ToggleDoor();
    }
    
    // Properties
    public bool IsOpen => isOpen;
    public bool IsPowered => isPowered;
    public bool RequiresKeycard => requiresKeycard;
    public bool IsMoving => isMoving;
    
    private void OnDestroy()
    {
        if (EventManager.Instance != null)
        {
            EventManager.Instance.Unsubscribe<PowerSystemChangedEvent>(OnPowerChanged);
        }
    }
    
    private void OnDrawGizmosSelected()
    {
        if (doorPanel != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireCube(doorPanel.position + openOffset, doorPanel.localScale);
            
            Gizmos.color = Color.red;
            Gizmos.DrawLine(doorPanel.position, doorPanel.position + openOffset);
        }
    }
}
