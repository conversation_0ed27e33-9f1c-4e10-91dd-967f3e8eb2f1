PropertyNodeView.hovered #selection-border{
    background-color:rgba(68,192,255,0.4);
    border-color:rgba(68,192,255,1);
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
}

PropertyNodeView > #disabledOverlay {
    position: absolute;
    left: 4;
    right: 4;
    top: 4;
    bottom: 4;
    background-color: rgba(32, 32, 32, 0);
}

PropertyNodeView.disabled #disabledOverlay {
    background-color: rgba(32, 32, 32, 0.5);
}

PropertyNodeView.disabled:hover #disabledOverlay {
    background-color: rgba(32, 32, 32, 0.25);
}

PropertyNodeView.disabled:checked #disabledOverlay {
    background-color: rgba(32, 32, 32, 0.25);
}
