using UnityEngine;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Manages all audio in the space game
/// </summary>
public class AudioManager : MonoBehaviour
{
    [Header("Audio Sources")]
    [SerializeField] private AudioSource musicSource;
    [SerializeField] private AudioSource sfxSource;
    [SerializeField] private AudioSource ambientSource;
    [SerializeField] private AudioSource uiSource;
    
    [Header("Audio Clips")]
    [SerializeField] private AudioClip[] musicTracks;
    [SerializeField] private AudioClip[] ambientSounds;
    
    [Header("Sound Effects")]
    [SerializeField] private AudioClip laserSound;
    [SerializeField] private AudioClip explosionSound;
    [SerializeField] private AudioClip powerupSound;
    [SerializeField] private AudioClip achievementSound;
    [SerializeField] private AudioClip upgradeSound;
    [SerializeField] private AudioClip missionSound;
    [SerializeField] private AudioClip footstepSound;
    [SerializeField] private AudioClip doorSound;
    [SerializeField] private AudioClip buttonClickSound;
    
    [Header("Settings")]
    [Range(0f, 1f)] public float masterVolume = 1f;
    [Range(0f, 1f)] public float musicVolume = 0.7f;
    [Range(0f, 1f)] public float sfxVolume = 1f;
    [Range(0f, 1f)] public float ambientVolume = 0.5f;
    [Range(0f, 1f)] public float uiVolume = 0.8f;
    
    public static AudioManager Instance { get; private set; }
    
    private Dictionary<string, AudioClip> soundEffects = new Dictionary<string, AudioClip>();
    private Coroutine musicFadeCoroutine;
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeAudio();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void InitializeAudio()
    {
        // Create audio sources if they don't exist
        if (musicSource == null)
            musicSource = CreateAudioSource("MusicSource");
        if (sfxSource == null)
            sfxSource = CreateAudioSource("SFXSource");
        if (ambientSource == null)
            ambientSource = CreateAudioSource("AmbientSource");
        if (uiSource == null)
            uiSource = CreateAudioSource("UISource");
        
        // Configure audio sources
        musicSource.loop = true;
        ambientSource.loop = true;
        
        // Initialize sound effects dictionary
        InitializeSoundEffects();
        
        // Apply volume settings
        UpdateVolumes();
    }
    
    private AudioSource CreateAudioSource(string name)
    {
        GameObject audioObject = new GameObject(name);
        audioObject.transform.SetParent(transform);
        return audioObject.AddComponent<AudioSource>();
    }
    
    private void InitializeSoundEffects()
    {
        if (laserSound != null) soundEffects["laser"] = laserSound;
        if (explosionSound != null) soundEffects["explosion"] = explosionSound;
        if (powerupSound != null) soundEffects["powerup"] = powerupSound;
        if (achievementSound != null) soundEffects["achievement"] = achievementSound;
        if (upgradeSound != null) soundEffects["upgrade"] = upgradeSound;
        if (missionSound != null) soundEffects["mission"] = missionSound;
        if (footstepSound != null) soundEffects["footstep"] = footstepSound;
        if (doorSound != null) soundEffects["door"] = doorSound;
        if (buttonClickSound != null) soundEffects["button_click"] = buttonClickSound;
    }
    
    public void UpdateVolumes()
    {
        if (musicSource != null)
            musicSource.volume = masterVolume * musicVolume;
        if (sfxSource != null)
            sfxSource.volume = masterVolume * sfxVolume;
        if (ambientSource != null)
            ambientSource.volume = masterVolume * ambientVolume;
        if (uiSource != null)
            uiSource.volume = masterVolume * uiVolume;
    }
    
    // Music Methods
    public void PlayMusic(int trackIndex)
    {
        if (musicTracks != null && trackIndex >= 0 && trackIndex < musicTracks.Length)
        {
            musicSource.clip = musicTracks[trackIndex];
            musicSource.Play();
        }
    }
    
    public void PlayMusic(AudioClip clip)
    {
        if (clip != null)
        {
            musicSource.clip = clip;
            musicSource.Play();
        }
    }
    
    public void StopMusic()
    {
        musicSource.Stop();
    }
    
    public void FadeMusic(float targetVolume, float duration)
    {
        if (musicFadeCoroutine != null)
            StopCoroutine(musicFadeCoroutine);
        musicFadeCoroutine = StartCoroutine(FadeMusicCoroutine(targetVolume, duration));
    }
    
    private IEnumerator FadeMusicCoroutine(float targetVolume, float duration)
    {
        float startVolume = musicSource.volume;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            musicSource.volume = Mathf.Lerp(startVolume, targetVolume * masterVolume * musicVolume, t);
            yield return null;
        }
        
        musicSource.volume = targetVolume * masterVolume * musicVolume;
    }
    
    // Sound Effects Methods
    public void PlaySFX(string soundName)
    {
        if (soundEffects.ContainsKey(soundName))
        {
            sfxSource.PlayOneShot(soundEffects[soundName]);
        }
        else
        {
            Debug.LogWarning($"Sound effect '{soundName}' not found!");
        }
    }
    
    public void PlaySFX(AudioClip clip)
    {
        if (clip != null)
        {
            sfxSource.PlayOneShot(clip);
        }
    }
    
    public void PlaySFX(AudioClip clip, float volume)
    {
        if (clip != null)
        {
            sfxSource.PlayOneShot(clip, volume);
        }
    }
    
    // Ambient Sound Methods
    public void PlayAmbient(int ambientIndex)
    {
        if (ambientSounds != null && ambientIndex >= 0 && ambientIndex < ambientSounds.Length)
        {
            ambientSource.clip = ambientSounds[ambientIndex];
            ambientSource.Play();
        }
    }
    
    public void PlayAmbient(AudioClip clip)
    {
        if (clip != null)
        {
            ambientSource.clip = clip;
            ambientSource.Play();
        }
    }
    
    public void StopAmbient()
    {
        ambientSource.Stop();
    }
    
    // UI Sound Methods
    public void PlayUI(AudioClip clip)
    {
        if (clip != null)
        {
            uiSource.PlayOneShot(clip);
        }
    }
    
    public void PlayButtonClick()
    {
        PlaySFX("button_click");
    }
    
    // 3D Audio Methods
    public void PlaySFX3D(AudioClip clip, Vector3 position, float volume = 1f)
    {
        if (clip != null)
        {
            AudioSource.PlayClipAtPoint(clip, position, volume * masterVolume * sfxVolume);
        }
    }
    
    // Utility Methods
    public void PauseAll()
    {
        musicSource.Pause();
        ambientSource.Pause();
    }
    
    public void ResumeAll()
    {
        musicSource.UnPause();
        ambientSource.UnPause();
    }
    
    public void StopAll()
    {
        musicSource.Stop();
        sfxSource.Stop();
        ambientSource.Stop();
        uiSource.Stop();
    }
}
