using UnityEngine;

/// <summary>
/// Defines an area where the player has access to oxygen
/// </summary>
public class OxygenZone : MonoBehaviour
{
    [Header("Oxygen Zone Settings")]
    [SerializeField] private bool providesOxygen = true;
    [SerializeField] private float oxygenLevel = 1f; // 0-1 multiplier for oxygen regeneration
    [SerializeField] private bool showGizmos = true;
    
    [Header("Visual Effects")]
    [SerializeField] private ParticleSystem oxygenParticles;
    [SerializeField] private AudioClip oxygenSound;
    [SerializeField] private Color gizmoColor = Color.cyan;
    
    private AudioSource audioSource;
    private Collider zoneCollider;
    
    private void Start()
    {
        zoneCollider = GetComponent<Collider>();
        if (zoneCollider == null)
        {
            Debug.LogWarning($"OxygenZone '{gameObject.name}' requires a Collider component!");
        }
        else
        {
            zoneCollider.isTrigger = true;
        }
        
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null && oxygenSound != null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
            audioSource.clip = oxygenSound;
            audioSource.loop = true;
            audioSource.playOnAwake = false;
            audioSource.volume = 0.3f;
        }
    }
    
    private void OnTriggerEnter(Collider other)
    {
        PlayerResources playerResources = other.GetComponent<PlayerResources>();
        if (playerResources != null)
        {
            playerResources.SetOxygenZone(providesOxygen);
            
            if (providesOxygen)
            {
                // Start oxygen effects
                if (oxygenParticles != null)
                    oxygenParticles.Play();
                    
                if (audioSource != null && oxygenSound != null)
                    audioSource.Play();
                    
                Debug.Log("Player entered oxygen zone");
            }
            else
            {
                Debug.Log("Player entered hazardous zone (no oxygen)");
            }
        }
    }
    
    private void OnTriggerExit(Collider other)
    {
        PlayerResources playerResources = other.GetComponent<PlayerResources>();
        if (playerResources != null)
        {
            playerResources.SetOxygenZone(!providesOxygen);
            
            if (providesOxygen)
            {
                // Stop oxygen effects
                if (oxygenParticles != null)
                    oxygenParticles.Stop();
                    
                if (audioSource != null)
                    audioSource.Stop();
                    
                Debug.Log("Player left oxygen zone");
            }
            else
            {
                Debug.Log("Player left hazardous zone");
            }
        }
    }
    
    private void OnDrawGizmos()
    {
        if (!showGizmos) return;
        
        Gizmos.color = gizmoColor;
        Gizmos.color = new Color(gizmoColor.r, gizmoColor.g, gizmoColor.b, 0.3f);
        
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            if (col is BoxCollider)
            {
                BoxCollider boxCol = col as BoxCollider;
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawCube(boxCol.center, boxCol.size);
            }
            else if (col is SphereCollider)
            {
                SphereCollider sphereCol = col as SphereCollider;
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawSphere(sphereCol.center, sphereCol.radius);
            }
            else if (col is CapsuleCollider)
            {
                CapsuleCollider capsuleCol = col as CapsuleCollider;
                Gizmos.matrix = transform.localToWorldMatrix;
                // Approximate capsule with sphere for simplicity
                Gizmos.DrawSphere(capsuleCol.center, capsuleCol.radius);
            }
        }
        
        // Draw wireframe
        Gizmos.color = new Color(gizmoColor.r, gizmoColor.g, gizmoColor.b, 1f);
        if (col is BoxCollider)
        {
            BoxCollider boxCol = col as BoxCollider;
            Gizmos.matrix = transform.localToWorldMatrix;
            Gizmos.DrawWireCube(boxCol.center, boxCol.size);
        }
        else if (col is SphereCollider)
        {
            SphereCollider sphereCol = col as SphereCollider;
            Gizmos.matrix = transform.localToWorldMatrix;
            Gizmos.DrawWireSphere(sphereCol.center, sphereCol.radius);
        }
    }
    
    private void OnDrawGizmosSelected()
    {
        // Draw additional info when selected
        Gizmos.color = Color.white;
        Vector3 labelPos = transform.position + Vector3.up * 2f;
        
        #if UNITY_EDITOR
        UnityEditor.Handles.Label(labelPos, $"Oxygen Zone\nProvides O2: {providesOxygen}\nLevel: {oxygenLevel:F1}");
        #endif
    }
}
