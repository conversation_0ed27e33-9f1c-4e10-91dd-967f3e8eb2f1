MaterialNodeView {
    overflow: visible;
}

MaterialNodeView.graphElement.node.MaterialNode {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
}

MaterialNodeView.master {
    min-width: 200px;
}

MaterialNodeView.blockData {
    width: 200px;
}

MaterialNodeView.blockData > #portInputContainer {
    top: 6px;
}

MaterialNodeView #collapsible-area {
    width: 0;
    height: 0;
}

MaterialNodeView #previewFiller.expanded {
    width: 200px;
    padding-bottom: 200px;
}

MaterialNodeView #previewFiller,
MaterialNodeView #controls {
    background-color: rgba(63, 63, 63, 0.8);
}

MaterialNodeView #controls > #items {
    padding-top: 4px;
    padding-bottom: 4px;
}

MaterialNodeView #title {
    padding-top: 8px;
    border-bottom-width: 8px;
}

MaterialNodeView > #previewContainer {
    position: absolute;
    bottom: 4px;
    left: 4px;
    border-radius: 6px;
    padding-top: 6px;
}

MaterialNodeView > #previewContainer > #preview  {
    width: 200px;
    height: 200px;
    align-items:center;
}

MaterialNodeView > #previewContainer > #preview > #collapse {
    background-color: #000;
    border-color: #F0F0F0;
    width: 0;
    height: 0;
    opacity: 0;
    border-radius: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    margin-top: 4px;
    align-items:center;
    justify-content:center;
}


MaterialNodeView:hover > #previewContainer > #preview > #collapse {
    width: 20px;
    height: 20px;
    opacity: 0.6;
}

MaterialNodeView > #previewContainer > #preview > #collapse > #icon  {
    background-image : resource("GraphView/Nodes/PreviewCollapse.png");
    width: 16px;
    height: 16px;
}

MaterialNodeView > #previewContainer > #preview > #collapse:hover {
    opacity: 1.0;
}

MaterialNodeView #previewFiller > #expand {
    align-self: center;
    width: 56px;
    height: 16px;
    flex-direction: row;
    justify-content:center;
}

MaterialNodeView #previewFiller > #expand > #icon {
    background-image : resource("GraphView/Nodes/PreviewExpand.png");
    width: 16px;
    height: 16px;
}

MaterialNodeView #previewFiller.collapsed > #expand:hover {
    background-color: #2B2B2B;
}

MaterialNodeView #previewFiller.expanded > #expand {
    height: 0;
}

MaterialNodeView > #resize {
    background-image : resource("GraphView/Nodes/NodeChevronLeft.png");
    position: absolute;
    right: 5px;
    bottom: 5px;
    width: 10px;
    height: 10px;
    cursor: resize-up-left;
}

MaterialNodeView PortInputView {
    position: absolute;
    left: -224px;
}

MaterialNodeView > #settings-container {
    background-color : rgb(63, 63, 63);
}

MaterialNodeView.hovered #selection-border{
    background-color:rgba(68,192,255,0.4);
    border-color:rgba(68,192,255,1);
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
}

#settings-button {
    width: 16px;
    justify-content: center;
    padding-left: 8px;
}

#settings-button > #icon {
    width : 12px;
    height : 12px;
    align-self: center;
    visibility: hidden;
    background-image : resource("Icons/SettingsIcons");
}

.node:hover #settings-button > #icon {
    visibility: visible;
}

#settings-button:hover > #icon {
    align-self: center;
    background-color: #2B2B2B;
    background-image : resource("Icons/SettingsIcons_hover");
}

#settings-button.clicked > #icon{
    background-color: #2B2B2B;
    background-image : resource("Icons/SettingsIcons_hover");
    visibility: visible;
}

.node.collapsed > #node-border > #title > #button-container > #collapse-button > #icon {
    background-image: resource("GraphView/Nodes/NodeChevronLeft.png");
}

.node.expanded > #node-border > #title > #button-container > #collapse-button > #icon {
    background-image : resource("GraphView/Nodes/NodeChevronDown.png");
}

MaterialNodeView > #disabledOverlay {
    border-radius: 4;
    position: absolute;
    left: 4;
    right: 4;
    top: 4;
    bottom: 4;
    background-color: rgba(32, 32, 32, 0);
}

MaterialNodeView.disabled #disabledOverlay {
    background-color: rgba(32, 32, 32, 0.5);
}

MaterialNodeView.disabled:hover #disabledOverlay {
    background-color: rgba(32, 32, 32, 0.25);
}

MaterialNodeView.disabled:checked #disabledOverlay {
    background-color: rgba(32, 32, 32, 0.25);
}
